# DailySync - SaaS Dashboard Platform

A full-stack SaaS dashboard platform designed for support agents and team members to log daily updates, track meetings, and visualize performance over time—with individual Slack integration and admin-level reporting.

## 🚀 Features

### Core Features
- **Daily Update Submission**: Users can manually submit daily activity using a structured form
- **Slack Integration**: Individual user Slack OAuth with automated message posting
- **Meeting Tracker**: Webhook endpoint for automatic meeting logging
- **Analytics Dashboard**: Personal and team-wide performance metrics
- **Role-based Access Control**: USER, ADMIN, and SUPERADMIN roles

### Tech Stack

**Frontend**
- React 18 with TypeScript
- TailwindCSS + Custom UI Components
- Zustand for state management
- React Router for navigation
- Recharts for data visualization

**Backend**
- NestJS with TypeScript
- Prisma ORM with PostgreSQL
- JWT Authentication
- Slack Web API integration
- Rate limiting and security middleware

## 📁 Project Structure

```
├── frontend/                 # React frontend application
│   ├── src/
│   │   ├── components/      # Reusable UI components
│   │   ├── pages/          # Page components
│   │   ├── services/       # API service layer
│   │   ├── store/          # Zustand state management
│   │   ├── types/          # TypeScript type definitions
│   │   └── lib/            # Utility functions
│   └── ...
├── backend/                  # NestJS backend application
│   ├── src/
│   │   ├── modules/        # Feature modules
│   │   │   ├── auth/       # Authentication
│   │   │   ├── update/     # Daily updates
│   │   │   ├── meeting/    # Meeting management
│   │   │   ├── slack/      # Slack integration
│   │   │   ├── analytics/  # Analytics & reporting
│   │   │   └── admin/      # Admin functionality
│   │   └── ...
│   ├── prisma/             # Database schema and migrations
│   └── ...
└── README.md
```

## 🛠️ Setup Instructions

### Prerequisites
- Node.js 18+ and npm
- PostgreSQL database
- Slack App (for Slack integration)

### 1. Clone and Install Dependencies

```bash
# Install frontend dependencies
cd frontend
npm install

# Install backend dependencies
cd ../backend
npm install
```

### 2. Database Setup

1. Create a PostgreSQL database
2. Update the `DATABASE_URL` in `backend/.env`:

```env
DATABASE_URL="postgresql://username:password@localhost:5432/dailysync?schema=public"
```

3. Run database migrations:

```bash
cd backend
npx prisma migrate dev --name init
npx prisma generate
```

### 3. Environment Configuration

**Backend (`backend/.env`)**
```env
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/dailysync?schema=public"

# JWT
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
JWT_EXPIRES_IN="7d"

# Slack (Optional - for Slack integration)
SLACK_CLIENT_ID="your-slack-client-id"
SLACK_CLIENT_SECRET="your-slack-client-secret"
SLACK_REDIRECT_URI="http://localhost:3001/auth/slack/callback"

# Encryption
ENCRYPTION_KEY="your-32-character-encryption-key-here"

# CORS
FRONTEND_URL="http://localhost:5173"

# Server
PORT=3001
NODE_ENV="development"
```

**Frontend (`frontend/.env`)**
```env
VITE_API_URL=http://localhost:3001
```

### 4. TailwindCSS Setup

The project uses TailwindCSS v4 with the new PostCSS plugin. The configuration is already set up correctly with:
- `@tailwindcss/postcss` plugin
- `@import "tailwindcss"` in the CSS file
- PostCSS configuration in `postcss.config.js`

### 5. Slack App Setup (Optional)

1. Create a Slack App at https://api.slack.com/apps
2. Add OAuth scopes: `chat:write`, `users:read`
3. Set redirect URL: `http://localhost:3001/auth/slack/callback`
4. Install the app to your workspace
5. Copy Client ID and Secret to backend `.env`

### 6. Run the Applications

**Backend:**
```bash
cd backend
npm run start:dev
```

**Frontend:**
```bash
cd frontend
npm run dev
```

The applications will be available at:
- Frontend: http://localhost:5173
- Backend API: http://localhost:3001

## 📊 API Endpoints

### Authentication
- `POST /auth/register` - User registration
- `POST /auth/login` - User login
- `GET /auth/profile` - Get user profile

### Updates
- `POST /updates` - Create daily update
- `GET /updates` - Get updates (filtered by user/date)
- `PATCH /updates/:id` - Update existing update
- `DELETE /updates/:id` - Delete update

### Meetings
- `POST /meetings/webhook` - Webhook for meeting creation
- `GET /meetings` - Get meetings (filtered by user/date)

### Analytics
- `GET /analytics` - Get analytics data
- `GET /analytics/leaderboard` - Get team leaderboard (Admin only)
- `GET /analytics/export` - Export data (Admin only)

### Slack
- `GET /slack/connect` - Get Slack OAuth URL
- `GET /slack/callback` - Handle Slack OAuth callback
- `POST /slack/disconnect` - Disconnect Slack
- `GET /slack/status` - Get Slack connection status

### Admin
- `GET /admin/users` - Get all users (Admin only)
- `PATCH /admin/users/:id/role` - Update user role (Admin only)
- `DELETE /admin/users/:id` - Delete user (SuperAdmin only)

## 🔧 Meeting Webhook

Send POST requests to `/meetings/webhook` with this format:

```json
{
  "userEmail": "<EMAIL>",
  "title": "Sprint Planning",
  "datetime": "2025-06-08T10:00:00Z",
  "timezone": "Asia/Dhaka",
  "meetLink": "https://meet.google.com/xyz-1234",
  "metadata": { "duration": "60min" }
}
```

## 🔐 User Roles

- **USER**: Submit updates, view own data
- **ADMIN**: View team data, analytics, manage users
- **SUPERADMIN**: Full access, user management, platform settings

## 🚀 Deployment

### Frontend (Vercel)
1. Connect your GitHub repo to Vercel
2. Set environment variables in Vercel dashboard
3. Deploy

### Backend (Railway/Render)
1. Connect your GitHub repo
2. Set environment variables
3. Add PostgreSQL database
4. Deploy

## 📝 Development Notes

- The application uses JWT for authentication
- Slack tokens are encrypted before storage
- Rate limiting is implemented for API endpoints
- Input validation is enforced on all endpoints
- CORS is configured for frontend-backend communication

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.
