import { ValidationOptions } from '../ValidationOptions';
import * as ValidatorJS from 'validator';
export declare const IS_EMAIL = "isEmail";
/**
 * Checks if the string is an email.
 * If given value is not a string, then it returns false.
 */
export declare function isEmail(value: unknown, options?: ValidatorJS.IsEmailOptions): boolean;
/**
 * Checks if the string is an email.
 * If given value is not a string, then it returns false.
 */
export declare function IsEmail(options?: ValidatorJS.IsEmailOptions, validationOptions?: ValidationOptions): PropertyDecorator;
