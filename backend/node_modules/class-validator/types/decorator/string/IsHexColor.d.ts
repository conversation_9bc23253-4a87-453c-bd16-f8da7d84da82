import { ValidationOptions } from '../ValidationOptions';
export declare const IS_HEX_COLOR = "isHexColor";
/**
 * Checks if the string is a hexadecimal color.
 * If given value is not a string, then it returns false.
 */
export declare function isHexColor(value: unknown): boolean;
/**
 * Checks if the string is a hexadecimal color.
 * If given value is not a string, then it returns false.
 */
export declare function IsHexColor(validationOptions?: ValidationOptions): PropertyDecorator;
