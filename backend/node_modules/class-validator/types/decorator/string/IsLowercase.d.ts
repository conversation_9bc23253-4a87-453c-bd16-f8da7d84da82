import { ValidationOptions } from '../ValidationOptions';
export declare const IS_LOWERCASE = "isLowercase";
/**
 * Checks if the string is lowercase.
 * If given value is not a string, then it returns false.
 */
export declare function isLowercase(value: unknown): boolean;
/**
 * Checks if the string is lowercase.
 * If given value is not a string, then it returns false.
 */
export declare function IsLowercase(validationOptions?: ValidationOptions): PropertyDecorator;
