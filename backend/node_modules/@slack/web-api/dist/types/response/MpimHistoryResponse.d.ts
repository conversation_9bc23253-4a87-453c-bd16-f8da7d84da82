import type { WebAPICallResult } from '../../WebClient';
export type MpimHistoryResponse = WebAPICallResult & {
    channel_actions_count?: number;
    error?: string;
    has_more?: boolean;
    messages?: Message[];
    needed?: string;
    ok?: boolean;
    provided?: string;
    response_metadata?: ResponseMetadata;
    warning?: string;
};
export interface Message {
    bot_id?: string;
    bot_profile?: BotProfile;
    is_starred?: boolean;
    last_read?: string;
    latest_reply?: string;
    parent_user_id?: string;
    reply_count?: number;
    reply_users?: string[];
    reply_users_count?: number;
    subscribed?: boolean;
    team?: string;
    text?: string;
    thread_ts?: string;
    ts?: string;
    type?: string;
    user?: string;
}
export interface BotProfile {
    app_id?: string;
    deleted?: boolean;
    icons?: Icons;
    id?: string;
    name?: string;
    team_id?: string;
    updated?: number;
}
export interface Icons {
    image_36?: string;
    image_48?: string;
    image_72?: string;
}
export interface ResponseMetadata {
    messages?: string[];
    warnings?: string[];
}
//# sourceMappingURL=MpimHistoryResponse.d.ts.map