import type { WebAPICallResult } from '../../WebClient';
export type RtmStartResponse = WebAPICallResult & {
    accept_tos_url?: string;
    bots?: Bot[];
    cache_ts?: number;
    cache_ts_version?: string;
    cache_version?: string;
    can_manage_shared_channels?: boolean;
    channels?: Channel[];
    dnd?: Dnd;
    error?: string;
    groups?: Group[];
    ims?: Im[];
    is_europe?: boolean;
    latest_event_ts?: string;
    links?: Links;
    mobile_app_requires_upgrade?: boolean;
    needed?: string;
    non_threadable_channels?: string[];
    ok?: boolean;
    provided?: string;
    read_only_channels?: string[];
    response_metadata?: ResponseMetadata;
    self?: Self;
    subteams?: Subteams;
    team?: Team;
    thread_only_channels?: string[];
    url?: string;
    users?: User[];
};
export interface Bot {
    app_id?: Appid;
    deleted?: boolean;
    icons?: BotIcons;
    id?: Id;
    is_workflow_bot?: boolean;
    name?: string;
    team_id?: string;
    updated?: number;
}
export declare enum Appid {
    A00000000 = "A00000000",
    Empty = ""
}
export interface BotIcons {
    image_36?: string;
    image_48?: string;
    image_72?: string;
}
export declare enum Id {
    B00000000 = "B00000000",
    Empty = ""
}
export interface Channel {
    connected_limited_team_ids?: string[];
    connected_team_ids?: string[];
    context_team_id?: string;
    conversation_host_id?: string;
    created?: number;
    creator?: string;
    has_pins?: boolean;
    id?: string;
    internal_team_ids?: string[];
    is_archived?: boolean;
    is_channel?: boolean;
    is_ext_shared?: boolean;
    is_general?: boolean;
    is_group?: boolean;
    is_im?: boolean;
    is_member?: boolean;
    is_mpim?: boolean;
    is_org_shared?: boolean;
    is_pending_ext_shared?: boolean;
    is_private?: boolean;
    is_shared?: boolean;
    last_read?: string;
    name?: string;
    name_normalized?: string;
    pending_connected_team_ids?: string[];
    pending_shared?: string[];
    previous_names?: string[];
    priority?: number;
    purpose?: Purpose;
    shared_team_ids?: string[];
    topic?: Purpose;
    unlinked?: number;
}
export interface Purpose {
    creator?: string;
    last_set?: number;
    value?: string;
}
export interface Dnd {
    dnd_enabled?: boolean;
    next_dnd_end_ts?: number;
    next_dnd_start_ts?: number;
    snooze_enabled?: boolean;
}
export interface Group {
    created?: number;
    creator?: string;
    id?: string;
    is_archived?: boolean;
    is_group?: boolean;
    is_mpim?: boolean;
    is_open?: boolean;
    is_read_only?: boolean;
    is_thread_only?: boolean;
    last_read?: string;
    latest?: Latest;
    members?: string[];
    name?: string;
    name_normalized?: string;
    parent_group?: string;
    priority?: number;
    purpose?: Purpose;
    topic?: Purpose;
    unread_count?: number;
    unread_count_display?: number;
}
export interface Latest {
    attachments?: Attachment[];
    blocks?: LatestBlock[];
    bot_id?: string;
    bot_link?: string;
    bot_profile?: Bot;
    client_msg_id?: string;
    display_as_bot?: boolean;
    edited?: Edited;
    files?: FileElement[];
    icons?: MessageIcons;
    parent_user_id?: string;
    reactions?: string[];
    root?: Root;
    subtype?: string;
    team?: string;
    text?: string;
    thread_ts?: string;
    topic?: string;
    ts?: string;
    type?: string;
    upload?: boolean;
    user?: string;
    username?: string;
    x_files?: string[];
}
export interface Attachment {
    actions?: Action[];
    app_id?: string;
    app_unfurl_url?: string;
    author_icon?: string;
    author_id?: string;
    author_link?: string;
    author_name?: string;
    author_subname?: string;
    blocks?: TitleBlockElement[];
    bot_id?: string;
    bot_team_id?: string;
    callback_id?: string;
    channel_id?: string;
    channel_name?: string;
    channel_team?: string;
    color?: string;
    fallback?: string;
    fields?: AttachmentField[];
    file_id?: string;
    filename?: string;
    files?: FileElement[];
    footer?: string;
    footer_icon?: string;
    from_url?: string;
    hide_border?: boolean;
    hide_color?: boolean;
    id?: number;
    image_bytes?: number;
    image_height?: number;
    image_url?: string;
    image_width?: number;
    indent?: boolean;
    is_app_unfurl?: boolean;
    is_file_attachment?: boolean;
    is_msg_unfurl?: boolean;
    is_reply_unfurl?: boolean;
    is_thread_root_unfurl?: boolean;
    list?: List;
    list_record?: PurpleListRecord;
    list_record_id?: string;
    list_records?: ListRecordElement[];
    list_schema?: Schema[];
    list_view?: View;
    list_view_id?: string;
    message_blocks?: MessageBlock[];
    metadata?: AttachmentMetadata;
    mimetype?: string;
    mrkdwn_in?: string[];
    msg_subtype?: string;
    original_url?: string;
    pretext?: string;
    preview?: AttachmentPreview;
    service_icon?: string;
    service_name?: string;
    service_url?: string;
    size?: number;
    text?: string;
    thumb_height?: number;
    thumb_url?: string;
    thumb_width?: number;
    title?: string;
    title_link?: string;
    ts?: string;
    url?: string;
    video_html?: string;
    video_html_height?: number;
    video_html_width?: number;
    video_url?: string;
}
export interface Action {
    confirm?: ActionConfirm;
    data_source?: string;
    id?: string;
    min_query_length?: number;
    name?: string;
    option_groups?: ActionOptionGroup[];
    options?: SelectedOptionElement[];
    selected_options?: SelectedOptionElement[];
    style?: string;
    text?: string;
    type?: ActionType;
    url?: string;
    value?: string;
}
export interface ActionConfirm {
    dismiss_text?: string;
    ok_text?: string;
    text?: string;
    title?: string;
}
export interface ActionOptionGroup {
    options?: SelectedOptionElement[];
    text?: string;
}
export interface SelectedOptionElement {
    text?: string;
    value?: string;
}
export declare enum ActionType {
    Button = "button",
    ChannelsSelect = "channels_select",
    Checkboxes = "checkboxes",
    ConversationsSelect = "conversations_select",
    Datepicker = "datepicker",
    Datetimepicker = "datetimepicker",
    ExternalSelect = "external_select",
    Image = "image",
    MultiChannelsSelect = "multi_channels_select",
    MultiConversationsSelect = "multi_conversations_select",
    MultiExternalSelect = "multi_external_select",
    MultiStaticSelect = "multi_static_select",
    MultiUsersSelect = "multi_users_select",
    Overflow = "overflow",
    RadioButtons = "radio_buttons",
    RichTextList = "rich_text_list",
    RichTextPreformatted = "rich_text_preformatted",
    RichTextQuote = "rich_text_quote",
    RichTextSection = "rich_text_section",
    StaticSelect = "static_select",
    Timepicker = "timepicker",
    UsersSelect = "users_select",
    WorkflowButton = "workflow_button"
}
export interface TitleBlockElement {
    accessory?: Accessory;
    alt_text?: string;
    app_collaborators?: string[];
    app_id?: string;
    author_name?: string;
    block_id?: string;
    bot_user_id?: string;
    button_label?: string;
    description?: DescriptionElement | string;
    developer_trace_id?: string;
    elements?: Accessory[];
    expand?: boolean;
    fallback?: string;
    fields?: DescriptionElement[];
    function_trigger_id?: string;
    image_bytes?: number;
    image_height?: number;
    image_url?: string;
    image_width?: number;
    is_animated?: boolean;
    is_workflow_app?: boolean;
    owning_team_id?: string;
    provider_icon_url?: string;
    provider_name?: string;
    sales_home_workflow_app_type?: number;
    share_url?: string;
    slack_file?: SlackFile;
    text?: DescriptionElement;
    thumbnail_url?: string;
    title?: DescriptionElement | string;
    title_url?: string;
    trigger_subtype?: string;
    trigger_type?: string;
    type?: BlockType;
    url?: string;
    video_url?: string;
    workflow_id?: string;
}
export interface Accessory {
    accessibility_label?: string;
    action_id?: string;
    alt_text?: string;
    border?: number;
    confirm?: AccessoryConfirm;
    default_to_current_conversation?: boolean;
    elements?: AccessoryElement[];
    fallback?: string;
    filter?: AccessoryFilter;
    focus_on_load?: boolean;
    image_bytes?: number;
    image_height?: number;
    image_url?: string;
    image_width?: number;
    indent?: number;
    initial_channel?: string;
    initial_channels?: string[];
    initial_conversation?: string;
    initial_conversations?: string[];
    initial_date?: string;
    initial_date_time?: number;
    initial_option?: InitialOptionElement;
    initial_options?: InitialOptionElement[];
    initial_time?: string;
    initial_user?: string;
    initial_users?: string[];
    max_selected_items?: number;
    min_query_length?: number;
    offset?: number;
    option_groups?: AccessoryOptionGroup[];
    options?: InitialOptionElement[];
    placeholder?: DescriptionElement;
    response_url_enabled?: boolean;
    slack_file?: SlackFile;
    style?: string;
    text?: DescriptionElement;
    timezone?: string;
    type?: ActionType;
    url?: string;
    value?: string;
    workflow?: Workflow;
}
export interface AccessoryConfirm {
    confirm?: DescriptionElement;
    deny?: DescriptionElement;
    style?: string;
    text?: DescriptionElement;
    title?: DescriptionElement;
}
export interface DescriptionElement {
    emoji?: boolean;
    text?: string;
    type?: DescriptionType;
    verbatim?: boolean;
}
export declare enum DescriptionType {
    Mrkdwn = "mrkdwn",
    PlainText = "plain_text"
}
export interface AccessoryElement {
    border?: number;
    elements?: PurpleElement[];
    indent?: number;
    offset?: number;
    style?: string;
    type?: ActionType;
}
export interface PurpleElement {
    channel_id?: string;
    fallback?: string;
    format?: string;
    name?: string;
    range?: string;
    skin_tone?: number;
    style?: Style;
    team_id?: string;
    text?: string;
    timestamp?: number;
    type?: PurpleType;
    unicode?: string;
    unsafe?: boolean;
    url?: string;
    user_id?: string;
    usergroup_id?: string;
    value?: string;
}
export interface Style {
    bold?: boolean;
    client_highlight?: boolean;
    code?: boolean;
    highlight?: boolean;
    italic?: boolean;
    strike?: boolean;
    unlink?: boolean;
}
export declare enum PurpleType {
    Broadcast = "broadcast",
    Channel = "channel",
    Color = "color",
    Date = "date",
    Emoji = "emoji",
    Link = "link",
    Team = "team",
    Text = "text",
    User = "user",
    Usergroup = "usergroup"
}
export interface AccessoryFilter {
    exclude_bot_users?: boolean;
    exclude_external_shared_channels?: boolean;
    include?: any[];
}
export interface InitialOptionElement {
    description?: DescriptionElement;
    text?: DescriptionElement;
    url?: string;
    value?: string;
}
export interface AccessoryOptionGroup {
    label?: DescriptionElement;
    options?: InitialOptionElement[];
}
export interface SlackFile {
    id?: string;
    url?: string;
}
export interface Workflow {
    trigger?: Trigger;
}
export interface Trigger {
    customizable_input_parameters?: CustomizableInputParameter[];
    url?: string;
}
export interface CustomizableInputParameter {
    name?: string;
    value?: string;
}
export declare enum BlockType {
    Actions = "actions",
    Context = "context",
    Divider = "divider",
    Image = "image",
    RichText = "rich_text",
    Section = "section",
    ShareShortcut = "share_shortcut",
    Video = "video"
}
export interface AttachmentField {
    short?: boolean;
    title?: string;
    value?: string;
}
export interface FileElement {
    access?: string;
    alt_txt?: string;
    app_id?: string;
    app_name?: string;
    attachments?: any[];
    blocks?: TitleBlockElement[];
    bot_id?: string;
    can_toggle_canvas_lock?: boolean;
    canvas_printing_enabled?: boolean;
    canvas_template_mode?: string;
    cc?: Cc[];
    channel_actions_count?: number;
    channel_actions_ts?: string;
    channels?: string[];
    comments_count?: number;
    converted_pdf?: string;
    created?: number;
    deanimate?: string;
    deanimate_gif?: string;
    display_as_bot?: boolean;
    dm_mpdm_users_with_file_access?: DmMpdmUsersWithFileAccess[];
    duration_ms?: number;
    edit_link?: string;
    edit_timestamp?: number;
    editable?: boolean;
    editor?: string;
    editors?: string[];
    external_id?: string;
    external_type?: string;
    external_url?: string;
    favorites?: Favorite[];
    file_access?: string;
    filetype?: string;
    from?: Cc[];
    groups?: string[];
    has_more?: boolean;
    has_more_shares?: boolean;
    has_rich_preview?: boolean;
    headers?: Headers;
    hls?: string;
    hls_embed?: string;
    id?: string;
    image_exif_rotation?: number;
    ims?: string[];
    initial_comment?: InitialComment;
    is_channel_space?: boolean;
    is_external?: boolean;
    is_public?: boolean;
    is_restricted_sharing_enabled?: boolean;
    is_starred?: boolean;
    last_editor?: string;
    last_read?: number;
    lines?: number;
    lines_more?: number;
    linked_channel_id?: string;
    list_csv_download_url?: string;
    list_limits?: ListLimits;
    list_metadata?: ListMetadata;
    media_display_type?: string;
    media_progress?: MediaProgress;
    mimetype?: string;
    mode?: string;
    mp4?: string;
    mp4_low?: string;
    name?: string;
    non_owner_editable?: boolean;
    num_stars?: number;
    org_or_workspace_access?: string;
    original_attachment_count?: number;
    original_h?: string;
    original_w?: string;
    permalink?: string;
    permalink_public?: string;
    pinned_to?: string[];
    pjpeg?: string;
    plain_text?: string;
    pretty_type?: string;
    preview?: string;
    preview_highlight?: string;
    preview_is_truncated?: boolean;
    preview_plain_text?: string;
    private_channels_with_file_access_count?: number;
    private_file_with_access_count?: number;
    public_url_shared?: boolean;
    quip_thread_id?: string;
    reactions?: Reaction[];
    saved?: Saved;
    sent_to_self?: boolean;
    shares?: PurpleShares;
    show_badge?: boolean;
    simplified_html?: string;
    size?: number;
    source_team?: string;
    subject?: string;
    subtype?: string;
    team_pref_version_history_enabled?: boolean;
    teams_shared_with?: any[];
    template_conversion_ts?: number;
    template_description?: string;
    template_icon?: string;
    template_name?: string;
    template_title?: string;
    thumb_1024?: string;
    thumb_1024_gif?: string;
    thumb_1024_h?: string;
    thumb_1024_w?: string;
    thumb_160?: string;
    thumb_160_gif?: string;
    thumb_160_h?: string;
    thumb_160_w?: string;
    thumb_360?: string;
    thumb_360_gif?: string;
    thumb_360_h?: string;
    thumb_360_w?: string;
    thumb_480?: string;
    thumb_480_gif?: string;
    thumb_480_h?: string;
    thumb_480_w?: string;
    thumb_64?: string;
    thumb_64_gif?: string;
    thumb_64_h?: string;
    thumb_64_w?: string;
    thumb_720?: string;
    thumb_720_gif?: string;
    thumb_720_h?: string;
    thumb_720_w?: string;
    thumb_80?: string;
    thumb_800?: string;
    thumb_800_gif?: string;
    thumb_800_h?: string;
    thumb_800_w?: string;
    thumb_80_gif?: string;
    thumb_80_h?: string;
    thumb_80_w?: string;
    thumb_960?: string;
    thumb_960_gif?: string;
    thumb_960_h?: string;
    thumb_960_w?: string;
    thumb_gif?: string;
    thumb_pdf?: string;
    thumb_pdf_h?: string;
    thumb_pdf_w?: string;
    thumb_tiny?: string;
    thumb_video?: string;
    thumb_video_h?: number;
    thumb_video_w?: number;
    timestamp?: number;
    title?: string;
    title_blocks?: TitleBlockElement[];
    to?: Cc[];
    transcription?: Transcription;
    update_notification?: number;
    updated?: number;
    url_private?: string;
    url_private_download?: string;
    url_static_preview?: string;
    user?: string;
    user_team?: string;
    username?: string;
    vtt?: string;
}
export interface Cc {
    address?: string;
    name?: string;
    original?: string;
}
export interface DmMpdmUsersWithFileAccess {
    access?: string;
    user_id?: string;
}
export interface Favorite {
    collection_id?: string;
    collection_name?: string;
    position?: string;
}
export interface Headers {
    date?: string;
    in_reply_to?: string;
    message_id?: string;
    reply_to?: string;
}
export interface InitialComment {
    channel?: string;
    comment?: string;
    created?: number;
    id?: string;
    is_intro?: boolean;
    timestamp?: number;
    user?: string;
}
export interface ListLimits {
    column_count?: number;
    column_count_limit?: number;
    max_attachments_per_cell?: number;
    over_column_maximum?: boolean;
    over_row_maximum?: boolean;
    over_view_maximum?: boolean;
    row_count?: number;
    row_count_limit?: number;
    view_count?: number;
    view_count_limit?: number;
}
export interface ListMetadata {
    creation_source?: CreationSource;
    description?: string;
    description_blocks?: TitleBlockElement[];
    icon?: string;
    icon_team_id?: string;
    icon_url?: string;
    integrations?: string[];
    is_trial?: boolean;
    schema?: Schema[];
    views?: View[];
}
export interface CreationSource {
    reference_id?: string;
    type?: string;
    workflow_function_id?: string;
}
export interface Schema {
    id?: string;
    is_primary_column?: boolean;
    key?: string;
    name?: string;
    options?: Options;
    type?: string;
}
export interface Options {
    canvas_id?: string;
    canvas_placeholder_mapping?: CanvasPlaceholderMapping[];
    choices?: Choice[];
    currency?: string;
    currency_format?: string;
    date_format?: string;
    default_value?: string;
    default_value_typed?: DefaultValueTyped;
    emoji?: string;
    emoji_team_id?: string;
    for_assignment?: boolean;
    format?: string;
    linked_to?: string[];
    mark_as_done_when_checked?: boolean;
    max?: number;
    notify_users?: boolean;
    precision?: number;
    rounding?: string;
    show_member_name?: boolean;
    time_format?: string;
}
export interface CanvasPlaceholderMapping {
    column?: string;
    variable?: string;
}
export interface Choice {
    color?: string;
    label?: string;
    value?: string;
}
export interface DefaultValueTyped {
    select?: string[];
}
export interface View {
    columns?: Column[];
    created_by?: string;
    date_created?: number;
    default_view_key?: string;
    filters?: FilterElement[];
    grouping?: Grouping;
    id?: string;
    is_all_items_view?: boolean;
    is_locked?: boolean;
    name?: string;
    position?: string;
    show_completed_items?: boolean;
    stick_column_left?: boolean;
    type?: string;
}
export interface Column {
    id?: string;
    key?: string;
    position?: string;
    visible?: boolean;
    width?: number;
}
export interface FilterElement {
    column_id?: string;
    key?: string;
    operator?: string;
    typed_values?: any[];
    values?: string[];
}
export interface Grouping {
    group_by?: string;
    group_by_column_id?: string;
}
export interface MediaProgress {
    duration_ms?: number;
    max_offset_ms?: number;
    media_watched?: boolean;
    offset_ms?: number;
}
export interface Reaction {
    count?: number;
    name?: string;
    url?: string;
    users?: string[];
}
export interface Saved {
    date_completed?: number;
    date_due?: number;
    is_archived?: boolean;
    state?: string;
}
export interface PurpleShares {
    private?: {
        [key: string]: Private[];
    };
    public?: {
        [key: string]: Private[];
    };
}
export interface Private {
    access?: string;
    channel_name?: string;
    date_last_shared?: number;
    latest_reply?: string;
    reply_count?: number;
    reply_users?: string[];
    reply_users_count?: number;
    share_user_id?: string;
    source?: string;
    team_id?: string;
    thread_ts?: string;
    ts?: string;
}
export interface Transcription {
    locale?: string;
    preview?: TranscriptionPreview;
    status?: string;
}
export interface TranscriptionPreview {
    content?: string;
    has_more?: boolean;
}
export interface List {
    channels?: string[];
    comments_count?: number;
    created?: number;
    display_as_bot?: boolean;
    dm_mpdm_users_with_file_access?: DmMpdmUsersWithFileAccess[];
    editable?: boolean;
    external_type?: string;
    file_access?: string;
    filetype?: string;
    groups?: string[];
    has_more_shares?: boolean;
    has_rich_preview?: boolean;
    id?: string;
    ims?: string[];
    is_external?: boolean;
    is_public?: boolean;
    last_editor?: string;
    list_limits?: ListLimits;
    list_metadata?: ListMetadata;
    mimetype?: string;
    mode?: string;
    name?: string;
    permalink?: string;
    permalink_public?: string;
    pretty_type?: string;
    private_channels_with_file_access_count?: number;
    private_file_with_access_count?: number;
    public_url_shared?: boolean;
    shares?: ListShares;
    size?: number;
    timestamp?: number;
    title?: string;
    updated?: number;
    url_private?: string;
    url_private_download?: string;
    user?: string;
    user_team?: string;
    username?: string;
}
export type ListShares = {};
export interface PurpleListRecord {
    record?: Record;
    schema?: Schema[];
}
export interface Record {
    fields?: RecordField[];
    record_id?: string;
}
export interface RecordField {
    attachment?: any[];
    channel?: any[];
    checkbox?: boolean;
    column_id?: string;
    date?: any[];
    email?: any[];
    key?: string;
    message?: Message;
    number?: any[];
    phone?: any[];
    rating?: any[];
    rich_text?: any[];
    select?: any[];
    text?: string;
    timestamp?: any[];
    user?: any[];
    value?: string;
}
export interface Message {
    app_id?: string;
    assistant_app_thread?: AssistantAppThread;
    attachments?: any[];
    blocks?: TitleBlockElement[];
    bot_id?: string;
    bot_link?: string;
    bot_profile?: Bot;
    channel?: string;
    client_msg_id?: string;
    comment?: Comment;
    display_as_bot?: boolean;
    edited?: Edited;
    file?: MessageFile;
    files?: any[];
    hidden?: boolean;
    icons?: MessageIcons;
    inviter?: string;
    is_intro?: boolean;
    is_locked?: boolean;
    is_starred?: boolean;
    is_thread_broadcast?: boolean;
    item?: Comment;
    item_type?: string;
    last_read?: string;
    latest_reply?: string;
    metadata?: MessageMetadata;
    no_notifications?: boolean;
    parent_user_id?: string;
    pinned_to?: any[];
    purpose?: string;
    reactions?: any[];
    replies?: any[];
    reply_count?: number;
    reply_users?: any[];
    reply_users_count?: number;
    room?: Room;
    root?: Root;
    subscribed?: boolean;
    subtype?: string;
    team?: string;
    text?: string;
    thread_ts?: string;
    topic?: string;
    ts?: string;
    type?: string;
    unfurl_links?: boolean;
    unfurl_media?: boolean;
    upload?: boolean;
    user?: string;
    username?: string;
    wibblr?: boolean;
    x_files?: any[];
}
export interface AssistantAppThread {
    first_user_thread_reply?: string;
    title?: string;
    title_blocks?: any[];
}
export interface Comment {
    comment?: string;
    created?: string;
    display_as_bot?: boolean;
    edit_link?: string;
    editable?: boolean;
    external_type?: string;
    filetype?: string;
    has_rich_preview?: boolean;
    id?: string;
    is_external?: boolean;
    is_intro?: boolean;
    is_public?: boolean;
    is_starred?: boolean;
    lines?: number;
    lines_more?: number;
    media_display_type?: string;
    mimetype?: string;
    mode?: string;
    name?: string;
    permalink?: string;
    permalink_public?: boolean;
    pretty_type?: string;
    preview?: string;
    preview_highlight?: string;
    preview_is_truncated?: boolean;
    public_url_shared?: boolean;
    size?: number;
    timestamp?: string;
    title?: string;
    url_private?: string;
    url_private_download?: boolean;
    user?: string;
    username?: string;
}
export interface Edited {
    ts?: string;
    user?: string;
}
export interface MessageFile {
    access?: string;
    alt_txt?: string;
    app_id?: string;
    app_name?: string;
    attachments?: any[];
    blocks?: any[];
    bot_id?: string;
    can_toggle_canvas_lock?: boolean;
    canvas_printing_enabled?: boolean;
    canvas_template_mode?: string;
    cc?: any[];
    channel_actions_count?: number;
    channel_actions_ts?: string;
    channels?: any[];
    comments_count?: number;
    converted_pdf?: string;
    created?: number;
    deanimate?: string;
    deanimate_gif?: string;
    display_as_bot?: boolean;
    dm_mpdm_users_with_file_access?: any[];
    duration_ms?: number;
    edit_link?: string;
    edit_timestamp?: number;
    editable?: boolean;
    editor?: string;
    editors?: any[];
    external_id?: string;
    external_type?: string;
    external_url?: string;
    favorites?: any[];
    file_access?: string;
    filetype?: string;
    from?: any[];
    groups?: any[];
    has_more?: boolean;
    has_more_shares?: boolean;
    has_rich_preview?: boolean;
    headers?: Headers;
    hls?: string;
    hls_embed?: string;
    id?: string;
    image_exif_rotation?: number;
    ims?: any[];
    initial_comment?: InitialComment;
    is_channel_space?: boolean;
    is_external?: boolean;
    is_public?: boolean;
    is_restricted_sharing_enabled?: boolean;
    is_starred?: boolean;
    last_editor?: string;
    last_read?: number;
    lines?: number;
    lines_more?: number;
    linked_channel_id?: string;
    list_csv_download_url?: string;
    list_limits?: ListLimits;
    list_metadata?: ListMetadata;
    media_display_type?: string;
    media_progress?: MediaProgress;
    mimetype?: string;
    mode?: string;
    mp4?: string;
    mp4_low?: string;
    name?: string;
    non_owner_editable?: boolean;
    num_stars?: number;
    org_or_workspace_access?: string;
    original_attachment_count?: number;
    original_h?: string;
    original_w?: string;
    permalink?: string;
    permalink_public?: string;
    pinned_to?: any[];
    pjpeg?: string;
    plain_text?: string;
    pretty_type?: string;
    preview?: string;
    preview_highlight?: string;
    preview_is_truncated?: boolean;
    preview_plain_text?: string;
    private_channels_with_file_access_count?: number;
    private_file_with_access_count?: number;
    public_url_shared?: boolean;
    quip_thread_id?: string;
    reactions?: any[];
    saved?: Saved;
    sent_to_self?: boolean;
    shares?: ListShares;
    show_badge?: boolean;
    simplified_html?: string;
    size?: number;
    source_team?: string;
    subject?: string;
    subtype?: string;
    team_pref_version_history_enabled?: boolean;
    teams_shared_with?: any[];
    template_conversion_ts?: number;
    template_description?: string;
    template_icon?: string;
    template_name?: string;
    template_title?: string;
    thumb_1024?: string;
    thumb_1024_gif?: string;
    thumb_1024_h?: string;
    thumb_1024_w?: string;
    thumb_160?: string;
    thumb_160_gif?: string;
    thumb_160_h?: string;
    thumb_160_w?: string;
    thumb_360?: string;
    thumb_360_gif?: string;
    thumb_360_h?: string;
    thumb_360_w?: string;
    thumb_480?: string;
    thumb_480_gif?: string;
    thumb_480_h?: string;
    thumb_480_w?: string;
    thumb_64?: string;
    thumb_64_gif?: string;
    thumb_64_h?: string;
    thumb_64_w?: string;
    thumb_720?: string;
    thumb_720_gif?: string;
    thumb_720_h?: string;
    thumb_720_w?: string;
    thumb_80?: string;
    thumb_800?: string;
    thumb_800_gif?: string;
    thumb_800_h?: string;
    thumb_800_w?: string;
    thumb_80_gif?: string;
    thumb_80_h?: string;
    thumb_80_w?: string;
    thumb_960?: string;
    thumb_960_gif?: string;
    thumb_960_h?: string;
    thumb_960_w?: string;
    thumb_gif?: string;
    thumb_pdf?: string;
    thumb_pdf_h?: string;
    thumb_pdf_w?: string;
    thumb_tiny?: string;
    thumb_video?: string;
    thumb_video_h?: number;
    thumb_video_w?: number;
    timestamp?: number;
    title?: string;
    title_blocks?: any[];
    to?: any[];
    transcription?: Transcription;
    update_notification?: number;
    updated?: number;
    url_private?: string;
    url_private_download?: string;
    url_static_preview?: string;
    user?: string;
    user_team?: string;
    username?: string;
    vtt?: string;
}
export interface MessageIcons {
    emoji?: string;
    image_36?: string;
    image_48?: string;
    image_64?: string;
    image_72?: string;
}
export interface MessageMetadata {
    event_type?: string;
}
export interface Room {
    app_id?: string;
    attached_file_ids?: any[];
    background_id?: string;
    call_family?: string;
    canvas_background?: string;
    canvas_thread_ts?: string;
    channels?: any[];
    created_by?: string;
    date_end?: number;
    date_start?: number;
    display_id?: string;
    external_unique_id?: string;
    has_ended?: boolean;
    huddle_link?: string;
    id?: string;
    is_dm_call?: boolean;
    is_prewarmed?: boolean;
    is_scheduled?: boolean;
    media_backend_type?: string;
    media_server?: string;
    name?: string;
    participant_history?: any[];
    participants?: any[];
    participants_camera_off?: any[];
    participants_camera_on?: any[];
    participants_screenshare_off?: any[];
    participants_screenshare_on?: any[];
    recording?: Recording;
    thread_root_ts?: string;
    was_accepted?: boolean;
    was_missed?: boolean;
    was_rejected?: boolean;
}
export interface Recording {
    can_record_summary?: string;
    notetaking?: boolean;
    summary?: boolean;
    summary_status?: string;
    transcript?: boolean;
}
export interface Root {
    bot_id?: string;
    bot_profile?: Bot;
    edited?: Edited;
    icons?: MessageIcons;
    last_read?: string;
    latest_reply?: string;
    mrkdwn?: boolean;
    no_notifications?: boolean;
    parent_user_id?: string;
    replies?: Edited[];
    reply_count?: number;
    reply_users?: string[];
    reply_users_count?: number;
    room?: Room;
    subscribed?: boolean;
    subtype?: string;
    team?: string;
    text?: string;
    thread_ts?: string;
    ts?: string;
    type?: string;
    unread_count?: number;
    user?: string;
    username?: string;
}
export interface ListRecordElement {
    created_by?: string;
    date_created?: number;
    fields?: RecordField[];
    id?: string;
    is_subscribed?: boolean;
    list_id?: string;
    platform_refs?: PlatformRefs;
    position?: string;
    saved?: Saved;
    thread_ts?: string;
    updated_by?: string;
    updated_timestamp?: string;
}
export interface PlatformRefs {
    bot_created_by?: string;
    bot_deleted_by?: string;
    bot_updated_by?: string;
}
export interface MessageBlock {
    channel?: string;
    message?: Message;
    team?: string;
    ts?: string;
}
export interface AttachmentMetadata {
    extension?: string;
    format?: string;
    original_h?: number;
    original_w?: number;
    rotation?: number;
    thumb_160?: boolean;
    thumb_360_h?: number;
    thumb_360_w?: number;
    thumb_64?: boolean;
    thumb_80?: boolean;
    thumb_tiny?: string;
}
export interface AttachmentPreview {
    can_remove?: boolean;
    icon_url?: string;
    subtitle?: DescriptionElement;
    title?: DescriptionElement;
    type?: string;
}
export interface LatestBlock {
    accessory?: Accessory;
    alt_text?: string;
    api_decoration_available?: boolean;
    app_collaborators?: string[];
    app_id?: string;
    author_name?: string;
    block_id?: string;
    bot_user_id?: string;
    button_label?: string;
    call?: Call;
    call_id?: string;
    description?: DescriptionElement;
    developer_trace_id?: string;
    dispatch_action?: boolean;
    element?: Accessory;
    elements?: Accessory[];
    expand?: boolean;
    external_id?: string;
    fallback?: string;
    fields?: DescriptionElement[];
    file?: FileElement;
    file_id?: string;
    function_trigger_id?: string;
    hint?: DescriptionElement;
    image_bytes?: number;
    image_height?: number;
    image_url?: string;
    image_width?: number;
    is_animated?: boolean;
    is_workflow_app?: boolean;
    label?: DescriptionElement;
    optional?: boolean;
    owning_team_id?: string;
    provider_icon_url?: string;
    provider_name?: string;
    sales_home_workflow_app_type?: number;
    share_url?: string;
    slack_file?: SlackFile;
    source?: string;
    text?: DescriptionElement;
    thumbnail_url?: string;
    title?: DescriptionElement;
    title_url?: string;
    trigger_subtype?: string;
    trigger_type?: string;
    type?: BlockType;
    url?: string;
    video_url?: string;
    workflow_id?: string;
}
export interface Call {
    media_backend_type?: string;
    v1?: V1;
}
export interface V1 {
    active_participants?: Participant[];
    all_participants?: Participant[];
    app_icon_urls?: AppIconUrls;
    app_id?: string;
    channels?: string[];
    created_by?: string;
    date_end?: number;
    date_start?: number;
    desktop_app_join_url?: string;
    display_id?: string;
    has_ended?: boolean;
    id?: string;
    is_dm_call?: boolean;
    join_url?: string;
    name?: string;
    was_accepted?: boolean;
    was_missed?: boolean;
    was_rejected?: boolean;
}
export interface Participant {
    avatar_url?: string;
    display_name?: string;
    external_id?: string;
    slack_id?: string;
}
export interface AppIconUrls {
    image_1024?: string;
    image_128?: string;
    image_192?: string;
    image_32?: string;
    image_36?: string;
    image_48?: string;
    image_512?: string;
    image_64?: string;
    image_72?: string;
    image_96?: string;
    image_original?: string;
}
export interface Im {
    context_team_id?: string;
    created?: number;
    has_pins?: boolean;
    id?: string;
    is_archived?: boolean;
    is_im?: boolean;
    is_open?: boolean;
    is_org_shared?: boolean;
    last_read?: string;
    priority?: number;
    user?: string;
}
export interface Links {
    domains_ts?: number;
}
export interface ResponseMetadata {
    messages?: string[];
}
export interface Self {
    created?: number;
    first_login?: number;
    id?: string;
    manual_presence?: string;
    name?: string;
    prefs?: SelfPrefs;
}
export interface SelfPrefs {
    a11y_animations?: boolean;
    a11y_dyslexic?: boolean;
    a11y_play_sound_for_incoming_dm?: boolean;
    a11y_play_sound_for_incoming_dm_choice?: string;
    a11y_play_sound_for_sent_dm?: boolean;
    a11y_play_sound_for_sent_dm_choice?: string;
    a11y_read_out_incoming_dm?: boolean;
    a11y_screen_reader_message_label_date_time_first?: boolean;
    activity_view?: string;
    add_apps_prompt_dismissed?: boolean;
    add_channel_prompt_dismissed?: boolean;
    add_prompt_interacted?: boolean;
    all_channels_loud?: boolean;
    all_notifications_prefs?: string;
    all_unreads_section_filter?: string;
    all_unreads_sort_order?: string;
    allow_calls_to_set_current_status?: boolean;
    allow_cmd_tab_iss?: boolean;
    allowed_unfurl_senders?: string;
    analytics_upsell_coachmark_seen?: boolean;
    app_manifest_schema_format?: string;
    app_subdomain_check_completed?: number;
    arrow_history?: boolean;
    at_channel_suppressed_channels?: string;
    box_enabled?: boolean;
    browsers_dismissed_channels_low_results_education?: boolean;
    browsers_dismissed_files_low_results_education?: boolean;
    browsers_dismissed_initial_activity_education?: boolean;
    browsers_dismissed_initial_drafts_education?: boolean;
    browsers_dismissed_initial_saved_education?: boolean;
    browsers_dismissed_people_low_results_education?: boolean;
    browsers_dismissed_user_groups_low_results_education?: boolean;
    browsers_seen_initial_activity_education?: boolean;
    browsers_seen_initial_channels_education?: boolean;
    browsers_seen_initial_drafts_education?: boolean;
    browsers_seen_initial_files_education?: boolean;
    browsers_seen_initial_people_education?: boolean;
    browsers_seen_initial_saved_education?: boolean;
    browsers_seen_initial_user_groups_education?: boolean;
    calls_disconnect_on_lock?: boolean;
    calls_survey_last_seen?: string;
    channel_canvas_variant?: number;
    channel_sections?: string;
    channel_sidebar_hide_browse_dms_link?: boolean;
    channel_sidebar_hide_invite?: boolean;
    channel_sort?: string;
    chime_access_check_completed?: number;
    clicked_close_onboarding_synth_view_banner?: boolean;
    client_logs_pri?: string;
    clips_feedback_survey_last_trigger_attempt?: number;
    color_names_in_list?: boolean;
    composer_nux?: string;
    confirm_clear_all_unreads?: boolean;
    confirm_sh_call_start?: boolean;
    confirm_user_marked_away?: boolean;
    connect_dm_early_access?: boolean;
    contextual_help_reset_count?: number;
    contextual_message_shortcuts_modal_was_seen?: boolean;
    convert_emoticons?: boolean;
    deprecation_banner_last_seen?: number;
    deprecation_modal_last_seen?: number;
    deprecation_toast_last_seen?: number;
    desktop_client_ids?: string;
    dismissed_app_config_redesign_coachmark?: boolean;
    dismissed_app_launcher_atlassian_promo?: boolean;
    dismissed_app_launcher_limit?: boolean;
    dismissed_app_launcher_welcome?: boolean;
    dismissed_app_manifest_coachmark?: boolean;
    dismissed_app_manifest_description?: boolean;
    dismissed_banner_download_ssb_prompt?: boolean;
    dismissed_connect_auto_approval_modal?: string;
    dismissed_fullscreen_download_ssb_prompt?: boolean;
    dismissed_gov_slack_first_time_popup?: boolean;
    dismissed_installed_app_dm_suggestions?: string;
    dismissed_scdm_education?: boolean;
    dismissed_scroll_search_tooltip_count?: number;
    dismissed_sent_page_education?: boolean;
    display_display_names?: boolean;
    display_real_names_override?: number;
    dnd_after_friday?: string;
    dnd_after_monday?: string;
    dnd_after_saturday?: string;
    dnd_after_sunday?: string;
    dnd_after_thursday?: string;
    dnd_after_tuesday?: string;
    dnd_after_wednesday?: string;
    dnd_before_friday?: string;
    dnd_before_monday?: string;
    dnd_before_saturday?: string;
    dnd_before_sunday?: string;
    dnd_before_thursday?: string;
    dnd_before_tuesday?: string;
    dnd_before_wednesday?: string;
    dnd_custom_new_badge_seen?: boolean;
    dnd_days?: string;
    dnd_enabled?: boolean;
    dnd_enabled_friday?: string;
    dnd_enabled_monday?: string;
    dnd_enabled_saturday?: string;
    dnd_enabled_sunday?: string;
    dnd_enabled_thursday?: string;
    dnd_enabled_tuesday?: string;
    dnd_enabled_wednesday?: string;
    dnd_end_hour?: string;
    dnd_notification_schedule_new_badge_seen?: boolean;
    dnd_start_hour?: string;
    dnd_weekdays_off_allday?: boolean;
    dropbox_enabled?: boolean;
    edge_upload_proxy_check_completed?: number;
    email_alerts?: string;
    email_alerts_sleep_until?: number;
    email_developer?: boolean;
    email_offers?: boolean;
    email_research?: boolean;
    email_tips?: boolean;
    email_weekly?: boolean;
    emoji_autocomplete_big?: boolean;
    emoji_mode?: string;
    emoji_packs_clicked_collection_cta?: boolean;
    emoji_packs_clicked_picker_cta?: boolean;
    emoji_packs_clicked_picker_post_install_cta?: boolean;
    emoji_packs_most_recent_available_time?: number;
    emoji_use?: string;
    emoji_use_org?: string;
    enable_all_dms_view?: boolean;
    enable_app_browser_view?: boolean;
    enable_app_config_redesign?: boolean;
    enable_channel_browser_view?: boolean;
    enable_drafts_view?: boolean;
    enable_file_browser_view?: boolean;
    enable_file_browser_view_for_docs?: boolean;
    enable_hq_view?: boolean;
    enable_later_view?: boolean;
    enable_media_captions?: boolean;
    enable_mentions_and_reactions_view?: boolean;
    enable_people_browser_view?: boolean;
    enable_quip_file_browser_view?: boolean;
    enable_react_emoji_picker?: boolean;
    enable_reminders_view?: boolean;
    enable_saved_items_view?: boolean;
    enable_scheduled_view?: boolean;
    enable_sent_view?: boolean;
    enable_shortcuts_view?: boolean;
    enable_slack_connect_view?: boolean;
    enable_slack_connect_view_2?: number;
    enable_streamline_view?: boolean;
    enable_unread_view?: boolean;
    ent_org_wide_channels_sidebar?: boolean;
    enter_is_special_in_tbt?: boolean;
    enterprise_mdm_custom_msg?: string;
    enterprise_migration_seen?: boolean;
    expand_inline_imgs?: boolean;
    expand_internal_inline_imgs?: boolean;
    expand_non_media_attachments?: boolean;
    expand_snippets?: boolean;
    f_key_search?: boolean;
    failover_proxy_check_completed?: number;
    file_picker_variant?: number;
    first_seen_new_badge_in_more_menu_sidebar?: number;
    first_seen_sent_page_in_sidebar?: number;
    flannel_server_pool?: string;
    folder_data?: string;
    folders_enabled?: boolean;
    frecency?: string;
    frecency_ent_jumper?: string;
    frecency_jumper?: string;
    fuller_timestamps?: boolean;
    graphic_emoticons?: boolean;
    growls_enabled?: boolean;
    growth_all_banners_prefs?: string;
    growth_msg_limit_approaching_cta_count?: number;
    growth_msg_limit_approaching_cta_ts?: number;
    growth_msg_limit_long_reached_cta_count?: number;
    growth_msg_limit_long_reached_cta_last_ts?: number;
    growth_msg_limit_reached_cta_count?: number;
    growth_msg_limit_reached_cta_last_ts?: number;
    growth_msg_limit_sixty_day_banner_cta_count?: number;
    growth_msg_limit_sixty_day_banner_cta_last_ts?: number;
    has_acknowledged_shortcut_speedbump?: boolean;
    has_created_channel?: boolean;
    has_created_channel_section?: boolean;
    has_dismissed_google_directory_coachmark?: boolean;
    has_drafted_message?: boolean;
    has_invited?: boolean;
    has_joined_huddle?: boolean;
    has_received_mention_or_reaction?: boolean;
    has_received_threaded_message?: boolean;
    has_recently_shared_a_channel?: boolean;
    has_searched?: boolean;
    has_sent_ten_messages?: number;
    has_starred_item?: boolean;
    has_uploaded?: boolean;
    has_used_quickswitcher_shortcut?: boolean;
    help_flexpane_clips_card_seen?: boolean;
    help_flexpane_slack_connect_card_seen?: boolean;
    help_menu_open_timestamp?: number;
    help_modal_consult_banner_dismissed?: boolean;
    help_modal_open_timestamp?: number;
    hidden_users?: string;
    hide_external_members_sharing_speed_bump?: boolean;
    hide_hex_swatch?: boolean;
    hide_user_group_info_pane?: boolean;
    highlight_words?: string;
    huddle_invite_sound?: string;
    huddle_survey_last_seen?: string;
    huddles_allow_smart_notif?: boolean;
    huddles_cc_by_default?: boolean;
    huddles_chime_new_endpoints_check_completed?: number;
    huddles_disconnect_on_lock?: boolean;
    huddles_global_mute?: boolean;
    huddles_mini_panel?: boolean;
    huddles_mute_by_default?: boolean;
    huddles_play_music_when_last?: boolean;
    huddles_reactions_play_sound?: boolean;
    huddles_reactions_read_out_loud?: boolean;
    huddles_set_status?: boolean;
    huddles_show_shouty_rooster?: boolean;
    huddles_variant?: number;
    ia_details_coachmark_seen?: boolean;
    ia_platform_actions_lab?: number;
    ia_slackbot_survey_timestamp_48h?: number;
    ia_slackbot_survey_timestamp_7d?: number;
    ia_top_nav_theme?: string;
    iap1_lab?: number;
    identity_links_global_prefs?: string;
    identity_links_prefs?: string;
    in_interactive_mas_migration_flow?: boolean;
    in_prod_surveys_enabled?: boolean;
    inbox_views_workspace_filter?: string;
    invite_accepted_notifications_muted?: boolean;
    join_calls_device_settings?: string;
    joiner_message_suggestion_dismissed?: boolean;
    joiner_notifications_muted?: boolean;
    jumbomoji?: boolean;
    k_key_omnibox?: boolean;
    k_key_omnibox_auto_hide_count?: number;
    last_dismissed_scroll_search_tooltip_timestamp?: number;
    last_seen_at_channel_warning?: number;
    last_seen_sections_unreads_only_prompt_timestamp?: number;
    last_snippet_type?: string;
    lessons_enabled?: boolean;
    load_lato_2?: boolean;
    locale?: string;
    locales_enabled?: LocalesEnabled;
    loud_channels?: string;
    loud_channels_set?: string;
    ls_disabled?: boolean;
    mac_ssb_bounce?: string;
    mac_ssb_bullet?: boolean;
    mark_msgs_read_immediately?: boolean;
    media_muted?: boolean;
    media_playback_speed?: number;
    media_volume?: number;
    mentions_exclude_at_channels?: boolean;
    mentions_exclude_at_user_groups?: boolean;
    mentions_exclude_reactions?: boolean;
    message_navigation_toast_was_seen?: boolean;
    messages_theme?: string;
    mobile_channel_list_show_all_dms?: boolean;
    mobile_channel_list_sort?: string;
    msg_input_send_btn?: boolean;
    msg_input_send_btn_auto_set?: boolean;
    msg_input_sticky_composer?: boolean;
    mute_sounds?: boolean;
    muted_channels?: string;
    mx_calendar_type?: string;
    needs_initial_password_set?: boolean;
    never_channels?: string;
    new_msg_snd?: string;
    newxp_seen_help_message?: number;
    newxp_seen_last_message?: number;
    newxp_suggested_channels?: string;
    no_created_overlays?: boolean;
    no_invites_widget_in_sidebar?: boolean;
    no_joined_overlays?: boolean;
    no_macelectron_banner?: boolean;
    no_macssb1_banner?: boolean;
    no_macssb2_banner?: boolean;
    no_omnibox_in_channels?: boolean;
    no_text_in_notifications?: boolean;
    no_winssb1_banner?: boolean;
    notification_center_filters?: string;
    notifications_view?: string;
    obey_inline_img_limit?: boolean;
    onboarding_cancelled?: boolean;
    onboarding_complete?: boolean;
    onboarding_slackbot_conversation_step?: number;
    onboarding_state?: number;
    onboarding_tip_opt_out?: boolean;
    open_quip_doc_in_flexpane?: boolean;
    opened_slackbot_dm?: boolean;
    overloaded_message_enabled?: boolean;
    pagekeys_handled?: boolean;
    phc_dismissed?: string;
    phc_viewed?: string;
    plain_text_mode?: boolean;
    posts_formatting_guide?: boolean;
    preferred_skin_tone?: string;
    privacy_policy_seen?: boolean;
    progressive_disclosure_state?: string;
    prompted_for_email_disabling?: boolean;
    purchaser?: boolean;
    push_at_channel_suppressed_channels?: string;
    push_dm_alert?: boolean;
    push_everything?: boolean;
    push_idle_wait?: number;
    push_loud_channels?: string;
    push_loud_channels_set?: string;
    push_mention_alert?: boolean;
    push_mention_channels?: string;
    push_show_preview?: boolean;
    push_sound?: string;
    reached_all_dms_disclosure?: boolean;
    reaction_notifications?: string;
    reminder_notification_time?: string;
    require_at?: boolean;
    saved_searches?: string;
    saved_view?: string;
    scdm_trial_offer_banner?: string;
    search_channel_sort?: string;
    search_exclude_bots?: boolean;
    search_exclude_channels?: string;
    search_file_sort?: string;
    search_hide_deactivated_users?: boolean;
    search_hide_my_channels?: boolean;
    search_only_current_team?: boolean;
    search_only_my_channels?: boolean;
    search_only_show_online?: boolean;
    search_people_sort?: string;
    search_sort?: string;
    seen_a11y_pref_setup_coachmark?: boolean;
    seen_administration_menu?: boolean;
    seen_app_space_coachmark?: boolean;
    seen_app_space_tutorial?: boolean;
    seen_bookmarks_intro?: boolean;
    seen_calls_interactive_coachmark?: boolean;
    seen_channel_browser_admin_coachmark?: boolean;
    seen_channel_email_tooltip?: boolean;
    seen_channel_search?: boolean;
    seen_connect_dm_coachmark?: boolean;
    seen_connect_section_coachmark?: boolean;
    seen_contextual_message_shortcuts_modal?: boolean;
    seen_corporate_export_alert?: boolean;
    seen_custom_status_badge?: boolean;
    seen_custom_status_callout?: boolean;
    seen_custom_status_expiration_badge?: boolean;
    seen_domain_invite_reminder?: boolean;
    seen_drafts_section_coachmark?: boolean;
    seen_edit_mode?: boolean;
    seen_edit_mode_edu?: boolean;
    seen_emoji_pack_cta?: number;
    seen_emoji_pack_dialog?: boolean;
    seen_emoji_update_overlay_coachmark?: boolean;
    seen_existing_install_coachmark?: boolean;
    seen_first_install_coachmark?: boolean;
    seen_floating_sidebar_coachmark?: boolean;
    seen_gdrive_coachmark?: boolean;
    seen_guest_admin_slackbot_announcement?: boolean;
    seen_highlights_arrows_coachmark?: boolean;
    seen_highlights_coachmark?: boolean;
    seen_highlights_warm_welcome?: boolean;
    seen_ia_education?: boolean;
    seen_intl_channel_names_coachmark?: boolean;
    seen_japanese_locale_change_message?: boolean;
    seen_keyboard_shortcuts_coachmark?: boolean;
    seen_link_unfurl_coachmark?: boolean;
    seen_locale_change_message?: number;
    seen_markdown_paste_shortcut?: number;
    seen_markdown_paste_toast?: number;
    seen_member_invite_reminder?: boolean;
    seen_message_navigation_educational_toast?: boolean;
    seen_name_tagging_coachmark?: boolean;
    seen_new_badge_in_more_menu_sidebar?: boolean;
    seen_new_search_ui?: boolean;
    seen_onboarding_banner?: boolean;
    seen_onboarding_channels?: boolean;
    seen_onboarding_direct_messages?: boolean;
    seen_onboarding_invites?: boolean;
    seen_onboarding_private_groups?: boolean;
    seen_onboarding_recent_mentions?: boolean;
    seen_onboarding_search?: boolean;
    seen_onboarding_slackbot_conversation?: boolean;
    seen_onboarding_starred_items?: boolean;
    seen_onboarding_start?: boolean;
    seen_onboarding_synth_view?: boolean;
    seen_onboarding_synth_view_count?: number;
    seen_onboarding_synth_view_count_v2?: number;
    seen_onboarding_synth_view_v2?: boolean;
    seen_p3_locale_change_message_ko_kr?: number;
    seen_people_search?: boolean;
    seen_people_search_count?: number;
    seen_quickswitcher_shortcut_tip_count?: number;
    seen_sc_menu_coachmark?: boolean;
    seen_sc_page?: boolean;
    seen_sc_page_banner?: boolean;
    seen_schedule_send_coachmark?: boolean;
    seen_sections_unreads_only_prompt_count?: number;
    seen_sent_page_in_sidebar?: boolean;
    seen_shared_channels_coachmark?: boolean;
    seen_shared_channels_opt_in_change_message?: boolean;
    seen_shdep_slackbot_message?: boolean;
    seen_single_emoji_msg?: boolean;
    seen_sonic_deluxe_toast?: number;
    seen_ssb_prompt?: boolean;
    seen_threads_notification_banner?: boolean;
    seen_toast_new_locale_launch?: string;
    seen_toast_new_locale_launch_ts?: number;
    seen_unread_view_coachmark?: boolean;
    seen_welcome_2?: boolean;
    seen_workflow_builder_deluxe_toast?: boolean;
    seen_wysiwyg_deluxe_toast?: boolean;
    separate_private_channels?: boolean;
    separate_shared_channels?: boolean;
    set_a11y_prefs_new_user?: boolean;
    set_tz_automatically?: boolean;
    shdep_promo_code_submitted?: boolean;
    should_show_connect_section?: boolean;
    should_show_contextual_help_for_conversation_navigation?: boolean;
    should_show_contextual_help_for_jump_to_conversation?: boolean;
    should_show_contextual_help_for_section_navigation?: boolean;
    should_show_contextual_help_for_thread_navigation?: boolean;
    should_show_unsend_message_confirmation?: boolean;
    show_all_skin_tones?: boolean;
    show_autocomplete_help?: number;
    show_ent_onboarding?: boolean;
    show_gov_slack_context_bar_banner?: boolean;
    show_ia_tour_relaunch?: number;
    show_jumper_scores?: boolean;
    show_memory_instrument?: boolean;
    show_quick_reactions?: boolean;
    show_shared_channels_education_banner?: boolean;
    show_sidebar_avatars?: boolean;
    show_sidebar_quickswitcher_button?: boolean;
    show_typing?: boolean;
    sidebar_behavior?: string;
    sidebar_pref_dismissed_tip?: boolean;
    sidebar_theme?: string;
    sidebar_theme_custom_values?: string;
    slack_connect_invite_should_badge_sidebar?: boolean;
    snippet_editor_wrap_long_lines?: boolean;
    spaces_new_xp_banner_dismissed?: boolean;
    ss_emojis?: boolean;
    ssb_space_window?: string;
    start_scroll_at_oldest?: boolean;
    suggestions_request_id?: string;
    sunset_interactive_message_views?: number;
    suppress_external_invites_from_compose_warning?: boolean;
    suppress_link_warning?: boolean;
    suppress_thread_mention_warning?: boolean;
    synth_view_prefs?: string;
    tab_ui_return_selects?: boolean;
    tasks_view?: string;
    threads_everything?: boolean;
    time24?: boolean;
    tractor_enabled?: boolean;
    tractor_experiment_group?: string;
    two_factor_auth_enabled?: boolean;
    tz?: string;
    underline_links?: boolean;
    up_to_browse_kb_shortcut?: boolean;
    used_custom_status_kb_shortcut?: boolean;
    user_colors?: string;
    user_customized_quick_reactions_display_feature?: number;
    user_customized_quick_reactions_has_customized?: boolean;
    user_customized_quick_reactions_use_frequently_used_emoji?: boolean;
    user_expectations_survey_last_trigger_attempt?: number;
    webapp_spellcheck?: boolean;
    welcome_message_hidden?: boolean;
    welcome_place_state?: string;
    whats_new_read?: number;
    who_can_see_account_by_searching_email?: string;
    who_can_share_contact_card?: string;
    whocanseethis_dm_mpdm_badge?: boolean;
    workflow_builder_coachmarks?: string;
    workflow_builder_intro_modal_clicked_through?: boolean;
    xws_dismissed_education?: boolean;
    xws_seen_education?: number;
    xws_sidebar_variant?: number;
}
export interface LocalesEnabled {
    'de-DE'?: string;
    'en-GB'?: string;
    'en-US'?: string;
    'es-ES'?: string;
    'es-LA'?: string;
    'fr-FR'?: string;
    'it-IT'?: string;
    'ja-JP'?: string;
    'ko-KR'?: string;
    'pt-BR'?: string;
    'ru-RU'?: string;
    'zh-CN'?: string;
    'zh-TW'?: string;
}
export interface Subteams {
    all?: All[];
    self?: string[];
}
export interface All {
    auto_provision?: boolean;
    channel_count?: number;
    created_by?: string;
    date_create?: number;
    date_delete?: number;
    date_update?: number;
    description?: string;
    enterprise_subteam_id?: string;
    handle?: string;
    id?: string;
    is_external?: boolean;
    is_subteam?: boolean;
    is_usergroup?: boolean;
    name?: string;
    prefs?: AllPrefs;
    team_id?: string;
    updated_by?: string;
    user_count?: number;
}
export interface AllPrefs {
    channels?: string[];
    groups?: Group[];
}
export interface Team {
    avatar_base_url?: string;
    date_create?: number;
    domain?: string;
    email_domain?: string;
    icon?: Icon;
    id?: string;
    is_verified?: boolean;
    limit_ts?: number;
    messages_count?: number;
    msg_edit_window_mins?: number;
    name?: string;
    onboarding_channel_id?: string;
    over_storage_limit?: boolean;
    plan?: string;
    prefs?: TeamPrefs;
    url?: string;
}
export interface Icon {
    image_102?: string;
    image_132?: string;
    image_230?: string;
    image_34?: string;
    image_44?: string;
    image_68?: string;
    image_88?: string;
    image_original?: string;
}
export interface TeamPrefs {
    admin_customized_quick_reactions?: string[];
    all_users_can_purchase?: boolean;
    allow_admin_retention_override?: number;
    allow_audio_clip_sharing_slack_connect?: boolean;
    allow_audio_clips?: boolean;
    allow_box_cfs?: boolean;
    allow_calls?: boolean;
    allow_calls_interactive_screen_sharing?: boolean;
    allow_clip_downloads?: string;
    allow_free_automated_trials?: boolean;
    allow_huddles?: boolean;
    allow_huddles_transcriptions?: boolean;
    allow_huddles_video?: boolean;
    allow_media_transcriptions?: boolean;
    allow_message_deletion?: boolean;
    allow_retention_override?: boolean;
    allow_spaceship?: string;
    allow_sponsored_slack_connections?: boolean;
    allow_video_clip_sharing_slack_connect?: boolean;
    allow_video_clips?: boolean;
    app_dir_only?: boolean;
    app_management_apps?: string[];
    app_whitelist_enabled?: boolean;
    auth_mode?: string;
    block_file_download?: boolean;
    box_app_installed?: boolean;
    calls_apps?: CallsApps;
    calls_locations?: string[];
    can_accept_slack_connect_channel_invites?: boolean;
    can_create_external_limited_invite?: boolean;
    can_create_slack_connect_channel_invite?: boolean;
    can_receive_shared_channels_invites?: boolean;
    channel_email_addresses_enabled?: boolean;
    compliance_export_start?: number;
    content_review_enabled?: boolean;
    created_with_google?: boolean;
    custom_status_default_emoji?: string;
    custom_status_presets?: Array<string[]>;
    daily_prompts_enabled?: boolean;
    default_channel_creation_enabled?: boolean;
    default_channels?: string[];
    default_create_private_channel?: boolean;
    default_rxns?: string[];
    disable_email_ingestion?: boolean;
    disable_file_deleting?: boolean;
    disable_file_editing?: boolean;
    disable_file_uploads?: string;
    disable_sidebar_connect_prompts?: string[];
    disable_sidebar_install_prompts?: string[];
    disallow_public_file_urls?: boolean;
    discoverable?: string;
    display_anniversary_celebration?: boolean;
    display_default_phone?: boolean;
    display_email_addresses?: boolean;
    display_external_email_addresses?: boolean;
    display_name_pronunciation?: boolean;
    display_new_hire_celebration?: boolean;
    display_pronouns?: boolean;
    display_real_names?: boolean;
    dm_retention_duration?: number;
    dm_retention_redaction_duration?: number;
    dm_retention_type?: number;
    dnd_after_friday?: string;
    dnd_after_monday?: string;
    dnd_after_saturday?: string;
    dnd_after_sunday?: string;
    dnd_after_thursday?: string;
    dnd_after_tuesday?: string;
    dnd_after_wednesday?: string;
    dnd_before_friday?: string;
    dnd_before_monday?: string;
    dnd_before_saturday?: string;
    dnd_before_sunday?: string;
    dnd_before_thursday?: string;
    dnd_before_tuesday?: string;
    dnd_before_wednesday?: string;
    dnd_days?: string;
    dnd_enabled?: boolean;
    dnd_enabled_friday?: string;
    dnd_enabled_monday?: string;
    dnd_enabled_saturday?: string;
    dnd_enabled_sunday?: string;
    dnd_enabled_thursday?: string;
    dnd_enabled_tuesday?: string;
    dnd_enabled_wednesday?: string;
    dnd_end_hour?: string;
    dnd_start_hour?: string;
    dnd_weekdays_off_allday?: boolean;
    dropbox_legacy_picker?: boolean;
    emoji_only_admins?: boolean;
    enable_connect_dm_early_access?: boolean;
    enable_domain_allowlist_for_cea?: boolean;
    enable_info_barriers?: boolean;
    enable_mpdm_to_private_channel_conversion?: boolean;
    enable_shared_channels?: number;
    enterprise_default_channels?: string[];
    enterprise_has_corporate_exports?: boolean;
    enterprise_intune_enabled?: boolean;
    enterprise_mandatory_channels?: string[];
    enterprise_mdm_date_enabled?: number;
    enterprise_mdm_disable_file_download?: boolean;
    enterprise_mdm_level?: number;
    enterprise_mdm_token?: string;
    enterprise_mobile_device_check?: boolean;
    enterprise_team_creation_request?: EnterpriseTeamCreationRequest;
    ext_audit_log_retention_duration?: number;
    ext_audit_log_retention_type?: number;
    file_limit_whitelisted?: boolean;
    file_retention_duration?: number;
    file_retention_type?: number;
    filepicker_app_first_install?: boolean;
    gdrive_enabled_team?: boolean;
    gg_enabled?: boolean;
    group_retention_duration?: number;
    group_retention_type?: number;
    has_compliance_export?: boolean;
    has_hipaa_compliance?: boolean;
    has_seen_partner_promo?: boolean;
    hermes_has_accepted_tos?: boolean;
    hermes_triggers_trippable_by_slack_connected_teams?: boolean;
    hide_gsuite_invite_option?: boolean;
    hide_referers?: boolean;
    identity_links_prefs?: EnterpriseTeamCreationRequest;
    invite_requests_enabled?: boolean;
    invited_user_preset?: InvitedUserPreset;
    invites_limit?: boolean;
    invites_only_admins?: boolean;
    joiner_notifications_enabled?: boolean;
    loading_only_admins?: boolean;
    locale?: string;
    loud_channel_mentions_limit?: number;
    magic_unfurls_enabled?: boolean;
    member_analytics_disabled?: boolean;
    ml_opt_out?: boolean;
    mobile_passcode_timeout_in_seconds?: number;
    mobile_session_duration?: number;
    msg_edit_window_mins?: number;
    notification_redaction_type?: string;
    notify_pending_enabled?: boolean;
    ntlm_credential_domains?: string;
    onedrive_app_installed?: boolean;
    onedrive_enabled_team?: boolean;
    private_channel_membership_limit?: number;
    private_retention_redaction_duration?: number;
    public_retention_redaction_duration?: number;
    received_esc_route_to_channel_awareness_message?: boolean;
    retention_duration?: number;
    retention_type?: number;
    rich_previews_default?: string;
    saml_enable?: boolean;
    search_feedback_opt_out?: boolean;
    self_serve_select?: boolean;
    session_duration?: number;
    session_duration_type?: number;
    show_join_leave?: boolean;
    show_legacy_paid_benefits_page?: boolean;
    show_mobile_promos?: boolean;
    sign_in_with_slack_default?: string;
    sign_in_with_slack_disabled?: boolean;
    single_user_exports?: boolean;
    slack_connect_account_visibility?: string;
    slack_connect_allowed_workspaces?: SlackConnectAllowedWorkspaces;
    slack_connect_approval_type?: string;
    slack_connect_dm_only_verified_orgs?: boolean;
    slack_connect_file_upload_sharing_enabled?: boolean;
    slackbot_responses_disabled?: boolean;
    slackbot_responses_only_admins?: boolean;
    spaceship_workspace_setting_visible?: boolean;
    sso_disable_emails?: boolean;
    sso_optional?: boolean;
    sso_signup_restrictions?: number;
    sso_sync_with_provider?: boolean;
    stats_only_admins?: boolean;
    subteams_auto_create_admin?: boolean;
    subteams_auto_create_owner?: boolean;
    thorn_safer_scan?: boolean;
    two_factor_auth_required?: number;
    uneditable_user_profile_fields?: string[];
    use_browser_picker?: boolean;
    uses_customized_custom_status_presets?: boolean;
    warn_before_at_channel?: string;
    warn_user_before_logout?: boolean;
    warn_user_before_logout_desktop?: boolean;
    warn_user_before_logout_mobile?: boolean;
    welcome_place_enabled?: boolean;
    who_can_accept_slack_connect_channel_invites?: SlackConnectAllowedWorkspaces;
    who_can_archive_channels?: string;
    who_can_at_channel?: string;
    who_can_at_everyone?: string;
    who_can_change_team_profile?: string;
    who_can_create_channel_email_addresses?: SlackConnectAllowedWorkspaces;
    who_can_create_channels?: string;
    who_can_create_delete_user_groups?: string;
    who_can_create_external_limited_invite?: SlackConnectAllowedWorkspaces;
    who_can_create_groups?: string;
    who_can_create_shared_channels?: string;
    who_can_create_slack_connect_channel_invite?: SlackConnectAllowedWorkspaces;
    who_can_create_workflows?: SlackConnectAllowedWorkspaces;
    who_can_dm_anyone?: SlackConnectAllowedWorkspaces;
    who_can_edit_user_groups?: string;
    who_can_kick_channels?: string;
    who_can_kick_groups?: string;
    who_can_manage_channel_posting_prefs?: string;
    who_can_manage_ext_shared_channels?: SlackConnectAllowedWorkspaces;
    who_can_manage_guests?: SlackConnectAllowedWorkspaces;
    who_can_manage_integrations?: SlackConnectAllowedWorkspaces;
    who_can_manage_private_channels?: WhoCanManageP;
    who_can_manage_private_channels_at_workspace_level?: WhoCanManageP;
    who_can_manage_public_channels?: WhoCanManageP;
    who_can_manage_shared_channels?: SlackConnectAllowedWorkspaces;
    who_can_post_general?: string;
    who_can_post_in_shared_channels?: SlackConnectAllowedWorkspaces;
    who_can_request_ext_shared_channels?: SlackConnectAllowedWorkspaces;
    who_can_review_flagged_content?: SlackConnectAllowedWorkspaces;
    who_can_use_hermes?: WhoCanUseHermes;
    who_can_view_message_activity?: SlackConnectAllowedWorkspaces;
    workflow_builder_enabled?: boolean;
    workflow_extension_steps_beta_opt_in?: boolean;
    workflow_extension_steps_enabled?: boolean;
    workflows_export_csv_enabled?: boolean;
    workflows_webhook_trigger_enabled?: boolean;
}
export interface CallsApps {
    audio?: string[];
    video?: Video[];
}
export interface Video {
    id?: string;
    image?: string;
    name?: string;
}
export interface EnterpriseTeamCreationRequest {
    is_enabled?: boolean;
}
export interface InvitedUserPreset {
    enable_invited_user?: boolean;
}
export interface SlackConnectAllowedWorkspaces {
    type?: string[];
}
export interface WhoCanManageP {
    type?: string[];
    user?: string[];
}
export interface WhoCanUseHermes {
    subteam?: string[];
    type?: string[];
    user?: string[];
}
export interface User {
    color?: string;
    deleted?: boolean;
    id?: string;
    is_admin?: boolean;
    is_app_user?: boolean;
    is_bot?: boolean;
    is_email_confirmed?: boolean;
    is_invited_user?: boolean;
    is_owner?: boolean;
    is_primary_owner?: boolean;
    is_restricted?: boolean;
    is_ultra_restricted?: boolean;
    is_workflow_bot?: boolean;
    name?: string;
    presence?: string;
    profile?: Profile;
    real_name?: string;
    team_id?: string;
    tz?: string;
    tz_label?: string;
    tz_offset?: number;
    updated?: number;
    who_can_share_contact_card?: string;
}
export interface Profile {
    always_active?: boolean;
    api_app_id?: string;
    avatar_hash?: string;
    bot_id?: Id;
    display_name?: string;
    display_name_normalized?: string;
    email?: string;
    fields?: Fields;
    first_name?: string;
    guest_invited_by?: string;
    huddle_state?: string;
    huddle_state_expiration_ts?: number;
    image_1024?: string;
    image_192?: string;
    image_24?: string;
    image_32?: string;
    image_48?: string;
    image_512?: string;
    image_72?: string;
    image_original?: string;
    is_custom_image?: boolean;
    last_name?: string;
    phone?: string;
    pronouns?: string;
    real_name?: string;
    real_name_normalized?: string;
    skype?: string;
    status_emoji?: string;
    status_emoji_display_info?: StatusEmojiDisplayInfo[];
    status_expiration?: number;
    status_text?: string;
    status_text_canonical?: string;
    team?: string;
    title?: string;
}
export interface Fields {
    Xf019LT13Z16?: Xf019LT13Z16;
}
export interface Xf019LT13Z16 {
    alt?: string;
    value?: string;
}
export interface StatusEmojiDisplayInfo {
    display_alias?: string;
    display_url?: string;
    emoji_name?: string;
    unicode?: string;
}
//# sourceMappingURL=RtmStartResponse.d.ts.map