"use strict";
/////////////////////////////////////////////////////////////////////////////////////////
//                                                                                     //
// !!! DO NOT EDIT THIS FILE !!!                                                       //
//                                                                                     //
// This file is auto-generated by scripts/generate-web-api-types.sh in the repository. //
// Please refer to the script code to learn how to update the source data.             //
//                                                                                     //
/////////////////////////////////////////////////////////////////////////////////////////
Object.defineProperty(exports, "__esModule", { value: true });
exports.ElementType = exports.CloseType = void 0;
var CloseType;
(function (CloseType) {
    CloseType["Empty"] = "";
    CloseType["Mrkdwn"] = "mrkdwn";
    CloseType["PlainText"] = "plain_text";
})(CloseType || (exports.CloseType = CloseType = {}));
var ElementType;
(function (ElementType) {
    ElementType["Broadcast"] = "broadcast";
    ElementType["Channel"] = "channel";
    ElementType["Color"] = "color";
    ElementType["Date"] = "date";
    ElementType["Emoji"] = "emoji";
    ElementType["Link"] = "link";
    ElementType["Team"] = "team";
    ElementType["Text"] = "text";
    ElementType["User"] = "user";
    ElementType["Usergroup"] = "usergroup";
})(ElementType || (exports.ElementType = ElementType = {}));
//# sourceMappingURL=ViewsPublishResponse.js.map