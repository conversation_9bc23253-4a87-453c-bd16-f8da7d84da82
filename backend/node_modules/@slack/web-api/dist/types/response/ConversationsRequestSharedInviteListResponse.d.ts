import type { WebAPICallResult } from '../../WebClient';
export type ConversationsRequestSharedInviteListResponse = WebAPICallResult & {
    error?: string;
    invite_requests?: InviteRequest[];
    needed?: string;
    ok?: boolean;
    provided?: string;
};
export interface InviteRequest {
    channel?: Channel;
    date_created?: number;
    date_last_updated?: number;
    expires_at?: number;
    id?: string;
    inviting_team?: Team;
    inviting_user?: InvitingUser;
    is_external_limited?: boolean;
    target_user?: TargetUser;
}
export interface Channel {
    connections?: Connection[];
    date_created?: number;
    id?: string;
    is_im?: boolean;
    is_private?: boolean;
    name?: string;
    pending_connections?: Connection[];
    previous_connections?: Connection[];
}
export interface Connection {
    is_private?: boolean;
    team?: Team;
}
export interface Team {
    avatar_base_url?: string;
    date_created?: number;
    domain?: string;
    icon?: Icon;
    id?: string;
    is_verified?: boolean;
    name?: string;
    requires_sponsorship?: boolean;
}
export interface Icon {
    image_102?: string;
    image_132?: string;
    image_230?: string;
    image_34?: string;
    image_44?: string;
    image_68?: string;
    image_88?: string;
    image_default?: boolean;
}
export interface InvitingUser {
    id?: string;
    name?: string;
    profile?: Profile;
    team_id?: string;
    updated?: number;
    who_can_share_contact_card?: string;
}
export interface Profile {
    avatar_hash?: string;
    display_name?: string;
    display_name_normalized?: string;
    email?: string;
    image_192?: string;
    image_24?: string;
    image_32?: string;
    image_48?: string;
    image_512?: string;
    image_72?: string;
    real_name?: string;
    real_name_normalized?: string;
    team?: string;
}
export interface TargetUser {
    recipient_email?: string;
}
//# sourceMappingURL=ConversationsRequestSharedInviteListResponse.d.ts.map