import type { WebAPICallResult } from '../../WebClient';
export type ChannelsListResponse = WebAPICallResult & {
    channels?: Channel[];
    error?: string;
    needed?: string;
    ok?: boolean;
    provided?: string;
    response_metadata?: ResponseMetadata;
    warning?: string;
};
export interface Channel {
    created?: number;
    creator?: string;
    id?: string;
    is_archived?: boolean;
    is_channel?: boolean;
    is_general?: boolean;
    is_member?: boolean;
    is_mpim?: boolean;
    is_org_shared?: boolean;
    is_private?: boolean;
    is_shared?: boolean;
    members?: string[];
    name?: string;
    name_normalized?: string;
    num_members?: number;
    previous_names?: string[];
    purpose?: Purpose;
    topic?: Purpose;
    unlinked?: number;
}
export interface Purpose {
    creator?: string;
    last_set?: number;
    value?: string;
}
export interface ResponseMetadata {
    messages?: string[];
    next_cursor?: string;
    warnings?: string[];
}
//# sourceMappingURL=ChannelsListResponse.d.ts.map