import type { WebAPICallResult } from '../../WebClient';
export type ImHistoryResponse = WebAPICallResult & {
    channel_actions_count?: number;
    error?: string;
    has_more?: boolean;
    messages?: Message[];
    needed?: string;
    ok?: boolean;
    provided?: string;
    response_metadata?: ResponseMetadata;
    warning?: string;
};
export interface Message {
    blocks?: Block[];
    bot_id?: string;
    bot_profile?: BotProfile;
    client_msg_id?: string;
    last_read?: string;
    latest_reply?: string;
    parent_user_id?: string;
    reply_count?: number;
    reply_users?: string[];
    reply_users_count?: number;
    subscribed?: boolean;
    team?: string;
    text?: string;
    thread_ts?: string;
    ts?: string;
    type?: string;
    user?: string;
}
export interface Block {
    accessory?: Accessory;
    alt_text?: string;
    block_id?: string;
    elements?: Element[];
    fallback?: string;
    fields?: Text[];
    image_bytes?: number;
    image_height?: number;
    image_url?: string;
    image_width?: number;
    text?: Text;
    title?: Text;
    type?: string;
}
export interface Accessory {
    alt_text?: string;
    fallback?: string;
    image_bytes?: number;
    image_height?: number;
    image_url?: string;
    image_width?: number;
    type?: string;
}
export interface Element {
    action_id?: string;
    alt_text?: string;
    confirm?: Confirm;
    default_to_current_conversation?: boolean;
    fallback?: string;
    filter?: Filter;
    image_bytes?: number;
    image_height?: number;
    image_url?: string;
    image_width?: number;
    initial_channel?: string;
    initial_conversation?: string;
    initial_date?: string;
    initial_option?: InitialOption;
    initial_user?: string;
    min_query_length?: number;
    placeholder?: Text;
    response_url_enabled?: boolean;
    style?: string;
    text?: Text;
    type?: string;
    url?: string;
    value?: string;
}
export interface Confirm {
    confirm?: Text;
    deny?: Text;
    style?: string;
    text?: Text;
    title?: Text;
}
export interface Text {
    emoji?: boolean;
    text?: string;
    type?: string;
    verbatim?: boolean;
}
export interface Filter {
    exclude_bot_users?: boolean;
    exclude_external_shared_channels?: boolean;
}
export interface InitialOption {
    description?: Text;
    text?: Text;
    url?: string;
    value?: string;
}
export interface BotProfile {
    app_id?: string;
    deleted?: boolean;
    icons?: Icons;
    id?: string;
    name?: string;
    team_id?: string;
    updated?: number;
}
export interface Icons {
    image_36?: string;
    image_48?: string;
    image_72?: string;
}
export interface ResponseMetadata {
    messages?: string[];
    warnings?: string[];
}
//# sourceMappingURL=ImHistoryResponse.d.ts.map