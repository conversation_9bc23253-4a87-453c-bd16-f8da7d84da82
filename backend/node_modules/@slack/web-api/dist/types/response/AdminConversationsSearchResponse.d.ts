import type { WebAPICallResult } from '../../WebClient';
export type AdminConversationsSearchResponse = WebAPICallResult & {
    conversations?: Conversation[];
    error?: string;
    needed?: string;
    next_cursor?: string;
    ok?: boolean;
    provided?: string;
    response_metadata?: ResponseMetadata;
    total_count?: number;
};
export interface Conversation {
    canvas?: ListsClass;
    channel_email_addresses?: ChannelEmailAddress[];
    channel_manager_count?: number;
    connected_limited_team_ids?: string[];
    connected_team_ids?: string[];
    context_team_id?: string;
    conversation_host_id?: string;
    created?: number;
    creator_id?: string;
    external_user_count?: number;
    id?: string;
    internal_team_ids?: string[];
    internal_team_ids_count?: number;
    internal_team_ids_sample_team?: string;
    is_archived?: boolean;
    is_disconnect_in_progress?: boolean;
    is_ext_shared?: boolean;
    is_frozen?: boolean;
    is_general?: boolean;
    is_global_shared?: boolean;
    is_org_default?: boolean;
    is_org_mandatory?: boolean;
    is_org_shared?: boolean;
    is_pending_ext_shared?: boolean;
    is_private?: boolean;
    last_activity_ts?: number;
    lists?: ListsClass;
    member_count?: number;
    name?: string;
    pending_connected_team_ids?: string[];
    properties?: Properties;
    purpose?: string;
}
export interface ListsClass {
    ownership_details?: OwnershipDetail[];
    total_count?: number;
}
export interface OwnershipDetail {
    count?: number;
    team_id?: string;
}
export interface ChannelEmailAddress {
    address?: string;
    conversation_id?: string;
    date_created?: number;
    icons?: Icons;
    name?: string;
    team_id?: string;
    user_id?: string;
}
export interface Icons {
    image_36?: string;
    image_48?: string;
    image_72?: string;
}
export interface Properties {
    at_channel_restricted?: boolean;
    at_here_restricted?: boolean;
    canvas?: PropertiesCanvas;
    huddles_restricted?: boolean;
    posting_restricted_to?: PostingRestrictedTo;
    tabs?: Tab[];
    tabz?: Tab[];
    threads_restricted_to?: ThreadsRestrictedTo;
}
export interface PropertiesCanvas {
    file_id?: string;
    is_empty?: boolean;
    quip_thread_id?: string;
}
export interface PostingRestrictedTo {
    type?: string[];
    user?: string[];
}
export interface Tab {
    id?: string;
    label?: string;
    type?: string;
}
export interface ThreadsRestrictedTo {
    type?: string[];
}
export interface ResponseMetadata {
    messages?: string[];
}
//# sourceMappingURL=AdminConversationsSearchResponse.d.ts.map