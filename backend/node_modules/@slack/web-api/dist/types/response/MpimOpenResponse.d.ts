import type { WebAPICallResult } from '../../WebClient';
export type MpimOpenResponse = WebAPICallResult & {
    error?: string;
    group?: Group;
    needed?: string;
    ok?: boolean;
    provided?: string;
    response_metadata?: ResponseMetadata;
    warning?: string;
};
export interface Group {
    created?: number;
    creator?: string;
    id?: string;
    is_archived?: boolean;
    is_group?: boolean;
    is_mpim?: boolean;
    is_open?: boolean;
    last_read?: string;
    latest?: Latest;
    members?: string[];
    name?: string;
    name_normalized?: string;
    priority?: number;
    purpose?: Purpose;
    topic?: Purpose;
    unread_count?: number;
    unread_count_display?: number;
}
export interface Latest {
    bot_id?: string;
    bot_profile?: BotProfile;
    parent_user_id?: string;
    team?: string;
    text?: string;
    thread_ts?: string;
    ts?: string;
    type?: string;
    user?: string;
}
export interface BotProfile {
    app_id?: string;
    deleted?: boolean;
    icons?: Icons;
    id?: string;
    name?: string;
    team_id?: string;
    updated?: number;
}
export interface Icons {
    image_36?: string;
    image_48?: string;
    image_72?: string;
}
export interface Purpose {
    creator?: string;
    last_set?: number;
    value?: string;
}
export interface ResponseMetadata {
    messages?: string[];
    warnings?: string[];
}
//# sourceMappingURL=MpimOpenResponse.d.ts.map