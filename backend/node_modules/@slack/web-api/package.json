{"name": "@slack/web-api", "version": "7.9.2", "description": "Official library for using the Slack Platform's Web API", "author": "Slack Technologies, LLC", "license": "MIT", "keywords": ["slack", "web-api", "bot", "client", "http", "api", "proxy", "rate-limiting", "pagination"], "main": "dist/index.js", "types": "./dist/index.d.ts", "files": ["dist/**/*"], "engines": {"node": ">= 18", "npm": ">= 8.6.0"}, "repository": "slackapi/node-slack-sdk", "homepage": "https://tools.slack.dev/node-slack-sdk/web-api", "publishConfig": {"access": "public"}, "bugs": {"url": "https://github.com/slackapi/node-slack-sdk/issues"}, "scripts": {"prepare": "npm run build", "build": "npm run build:clean && tsc", "build:clean": "shx rm -rf ./dist ./coverage", "lint": "npx @biomejs/biome check .", "lint:fix": "npx @biomejs/biome check --write .", "mocha": "mocha --config ./test/.mocharc.json \"./src/**/*.spec.ts\"", "test": "npm run lint && npm run test:types && npm run test:integration && npm run test:unit", "test:integration": "npm run build && node test/integration/commonjs-project/index.js && node test/integration/esm-project/index.mjs && npm run test:integration:ts", "test:integration:ts": "cd test/integration/ts-4.7-project && npm i && npm run build", "test:unit": "npm run build && c8 --config ./test/.c8rc.json npm run mocha", "test:types": "tsd", "watch": "npx nodemon --watch 'src' --ext 'ts' --exec npm run build"}, "dependencies": {"@slack/logger": "^4.0.0", "@slack/types": "^2.9.0", "@types/node": ">=18.0.0", "@types/retry": "0.12.0", "axios": "^1.8.3", "eventemitter3": "^5.0.1", "form-data": "^4.0.0", "is-electron": "2.2.2", "is-stream": "^2", "p-queue": "^6", "p-retry": "^4", "retry": "^0.13.1"}, "devDependencies": {"@biomejs/biome": "^1.8.3", "@tsconfig/recommended": "^1", "@types/busboy": "^1.5.4", "@types/chai": "^4", "@types/mocha": "^10", "@types/sinon": "^17", "busboy": "^1", "c8": "^10.1.2", "chai": "^4", "mocha": "^11", "mocha-junit-reporter": "^2.2.1", "mocha-multi-reporters": "^1.5.1", "nock": "^14", "shx": "^0.4.0", "sinon": "^20", "source-map-support": "^0.5.21", "ts-node": "^10", "tsd": "^0.32.0", "typescript": "5.8.3"}, "tsd": {"directory": "test/types"}}