import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { User, Role } from '@prisma/client';

@Injectable()
export class AdminService {
  constructor(private prisma: PrismaService) {}

  async getAllUsers(): Promise<User[]> {
    return this.prisma.user.findMany({
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  async getUserById(id: string): Promise<User> {
    const user = await this.prisma.user.findUnique({
      where: { id },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return user;
  }

  async updateUserRole(id: string, role: Role, adminRole: Role): Promise<User> {
    // Only SUPERADMIN can promote to ADMIN or SUPERADMIN
    if ((role === 'ADMIN' || role === 'SUPERADMIN') && adminRole !== 'SUPERADMIN') {
      throw new ForbiddenException('Only SUP<PERSON><PERSON><PERSON><PERSON> can assign ADMIN or SUPERADMIN roles');
    }

    const user = await this.prisma.user.findUnique({
      where: { id },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return this.prisma.user.update({
      where: { id },
      data: { role },
    });
  }

  async deleteUser(id: string, adminRole: Role): Promise<void> {
    const user = await this.prisma.user.findUnique({
      where: { id },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Prevent deletion of SUPERADMIN by non-SUPERADMIN
    if (user.role === 'SUPERADMIN' && adminRole !== 'SUPERADMIN') {
      throw new ForbiddenException('Only SUPERADMIN can delete SUPERADMIN users');
    }

    await this.prisma.user.delete({
      where: { id },
    });
  }

  async getUserStats(): Promise<{
    totalUsers: number;
    usersByRole: Record<Role, number>;
    recentUsers: User[];
  }> {
    const totalUsers = await this.prisma.user.count();
    
    const usersByRole = await this.prisma.user.groupBy({
      by: ['role'],
      _count: {
        role: true,
      },
    });

    const roleStats = usersByRole.reduce((acc, item) => {
      acc[item.role] = item._count.role;
      return acc;
    }, {} as Record<Role, number>);

    // Ensure all roles are represented
    const allRoles: Role[] = ['USER', 'ADMIN', 'SUPERADMIN'];
    allRoles.forEach(role => {
      if (!roleStats[role]) {
        roleStats[role] = 0;
      }
    });

    const recentUsers = await this.prisma.user.findMany({
      take: 10,
      orderBy: {
        createdAt: 'desc',
      },
    });

    return {
      totalUsers,
      usersByRole: roleStats,
      recentUsers,
    };
  }
}
