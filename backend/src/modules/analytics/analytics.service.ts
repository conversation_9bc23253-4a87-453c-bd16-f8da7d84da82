import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';

export interface AnalyticsData {
  totalTickets: number;
  totalChats: number;
  totalIssues: number;
  totalReviews: number;
  totalMeetings: number;
  dailyStats: Array<{
    date: string;
    tickets: number;
    chats: number;
    issues: number;
    reviews: number;
    meetings: number;
  }>;
}

export interface LeaderboardEntry {
  user: {
    id: string;
    name: string;
    email: string;
  };
  totalTickets: number;
  totalChats: number;
  totalIssues: number;
  totalReviews: number;
  totalScore: number;
}

@Injectable()
export class AnalyticsService {
  constructor(private prisma: PrismaService) {}

  async getAnalytics(
    userId?: string,
    startDate?: string,
    endDate?: string,
  ): Promise<AnalyticsData> {
    const where: any = {};

    if (userId) {
      where.userId = userId;
    }

    if (startDate || endDate) {
      where.date = {};
      if (startDate) {
        where.date.gte = new Date(startDate);
      }
      if (endDate) {
        where.date.lte = new Date(endDate);
      }
    }

    // Get updates
    const updates = await this.prisma.update.findMany({
      where,
      orderBy: { date: 'asc' },
    });

    // Get meetings
    const meetingWhere: any = {};
    if (userId) {
      meetingWhere.userId = userId;
    }
    if (startDate || endDate) {
      meetingWhere.date = {};
      if (startDate) {
        meetingWhere.date.gte = new Date(startDate);
      }
      if (endDate) {
        meetingWhere.date.lte = new Date(endDate);
      }
    }

    const meetings = await this.prisma.meeting.findMany({
      where: meetingWhere,
    });

    // Calculate totals
    const totalTickets = updates.reduce((sum, update) => sum + update.tickets, 0);
    const totalChats = updates.reduce((sum, update) => sum + update.chats, 0);
    const totalIssues = updates.reduce((sum, update) => sum + update.issues, 0);
    const totalReviews = updates.reduce((sum, update) => sum + update.reviews, 0);
    const totalMeetings = meetings.length;

    // Group by date for daily stats
    const dailyStatsMap = new Map<string, any>();

    updates.forEach(update => {
      const dateKey = update.date.toISOString().split('T')[0];
      if (!dailyStatsMap.has(dateKey)) {
        dailyStatsMap.set(dateKey, {
          date: dateKey,
          tickets: 0,
          chats: 0,
          issues: 0,
          reviews: 0,
          meetings: 0,
        });
      }
      const dayStats = dailyStatsMap.get(dateKey);
      dayStats.tickets += update.tickets;
      dayStats.chats += update.chats;
      dayStats.issues += update.issues;
      dayStats.reviews += update.reviews;
    });

    meetings.forEach(meeting => {
      const dateKey = meeting.date.toISOString().split('T')[0];
      if (!dailyStatsMap.has(dateKey)) {
        dailyStatsMap.set(dateKey, {
          date: dateKey,
          tickets: 0,
          chats: 0,
          issues: 0,
          reviews: 0,
          meetings: 0,
        });
      }
      const dayStats = dailyStatsMap.get(dateKey);
      dayStats.meetings += 1;
    });

    const dailyStats = Array.from(dailyStatsMap.values()).sort((a, b) => 
      new Date(a.date).getTime() - new Date(b.date).getTime()
    );

    return {
      totalTickets,
      totalChats,
      totalIssues,
      totalReviews,
      totalMeetings,
      dailyStats,
    };
  }

  async getLeaderboard(
    startDate?: string,
    endDate?: string,
  ): Promise<LeaderboardEntry[]> {
    const where: any = {};

    if (startDate || endDate) {
      where.date = {};
      if (startDate) {
        where.date.gte = new Date(startDate);
      }
      if (endDate) {
        where.date.lte = new Date(endDate);
      }
    }

    const updates = await this.prisma.update.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    // Group by user
    const userStatsMap = new Map<string, LeaderboardEntry>();

    updates.forEach(update => {
      const userId = update.user.id;
      if (!userStatsMap.has(userId)) {
        userStatsMap.set(userId, {
          user: update.user,
          totalTickets: 0,
          totalChats: 0,
          totalIssues: 0,
          totalReviews: 0,
          totalScore: 0,
        });
      }

      const userStats = userStatsMap.get(userId)!;
      userStats.totalTickets += update.tickets;
      userStats.totalChats += update.chats;
      userStats.totalIssues += update.issues;
      userStats.totalReviews += update.reviews;
    });

    // Calculate scores and sort
    const leaderboard = Array.from(userStatsMap.values()).map(entry => {
      // Simple scoring system - you can adjust weights as needed
      entry.totalScore = 
        entry.totalTickets * 1 +
        entry.totalChats * 1 +
        entry.totalIssues * 2 +
        entry.totalReviews * 3;
      return entry;
    });

    return leaderboard.sort((a, b) => b.totalScore - a.totalScore);
  }

  async exportData(
    userId?: string,
    startDate?: string,
    endDate?: string,
    format: 'csv' | 'json' = 'json',
  ): Promise<any> {
    const analytics = await this.getAnalytics(userId, startDate, endDate);
    
    if (format === 'csv') {
      // Convert to CSV format
      const csvHeaders = 'Date,Tickets,Chats,Issues,Reviews,Meetings\n';
      const csvData = analytics.dailyStats
        .map(day => `${day.date},${day.tickets},${day.chats},${day.issues},${day.reviews},${day.meetings}`)
        .join('\n');
      return csvHeaders + csvData;
    }

    return analytics;
  }
}
