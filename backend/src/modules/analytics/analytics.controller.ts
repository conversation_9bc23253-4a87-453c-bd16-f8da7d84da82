import {
  Controller,
  Get,
  UseGuards,
  Request,
  Query,
  Res,
} from '@nestjs/common';
import { Response } from 'express';
import { AnalyticsService, AnalyticsData, LeaderboardEntry } from './analytics.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';

@Controller('analytics')
@UseGuards(JwtAuthGuard)
export class AnalyticsController {
  constructor(private readonly analyticsService: AnalyticsService) {}

  @Get()
  getAnalytics(
    @Request() req,
    @Query('userId') userId?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ): Promise<AnalyticsData> {
    // Regular users can only see their own analytics
    const finalUserId = req.user.role === 'USER' ? req.user.userId : userId;
    return this.analyticsService.getAnalytics(finalUserId, startDate, endDate);
  }

  @Get('leaderboard')
  @UseGuards(RolesGuard)
  @Roles('ADMIN', 'SUPERADMIN')
  getLeaderboard(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ): Promise<LeaderboardEntry[]> {
    return this.analyticsService.getLeaderboard(startDate, endDate);
  }

  @Get('export')
  @UseGuards(RolesGuard)
  @Roles('ADMIN', 'SUPERADMIN')
  async exportData(
    @Query('userId') userId: string | undefined,
    @Query('startDate') startDate: string | undefined,
    @Query('endDate') endDate: string | undefined,
    @Query('format') format: 'csv' | 'json' = 'json',
    @Res() res: Response,
  ) {
    const data = await this.analyticsService.exportData(userId, startDate, endDate, format);

    if (format === 'csv') {
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', 'attachment; filename=analytics.csv');
      res.send(data);
    } else {
      res.json(data);
    }
  }
}
