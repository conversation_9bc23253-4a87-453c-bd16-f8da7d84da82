import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../prisma/prisma.service';
import { WebClient } from '@slack/web-api';
import { Update, User } from '@prisma/client';
import * as crypto from 'crypto';

@Injectable()
export class SlackService {
  private readonly encryptionKey: string;

  constructor(
    private configService: ConfigService,
    private prisma: PrismaService,
  ) {
    this.encryptionKey = this.configService.get<string>('ENCRYPTION_KEY') || 'default-encryption-key-32-chars!!';
  }

  private encrypt(text: string): string {
    // Using a simple base64 encoding for now - in production use proper encryption
    return Buffer.from(text).toString('base64');
  }

  private decrypt(encryptedText: string): string {
    // Using a simple base64 decoding for now - in production use proper decryption
    return Buffer.from(encryptedText, 'base64').toString('utf8');
  }

  async getAuthUrl(): Promise<string> {
    const clientId = this.configService.get<string>('SLACK_CLIENT_ID') || '';
    const redirectUri = this.configService.get<string>('SLACK_REDIRECT_URI') || '';
    const scopes = 'chat:write,users:read';

    return `https://slack.com/oauth/v2/authorize?client_id=${clientId}&scope=${scopes}&redirect_uri=${encodeURIComponent(redirectUri)}`;
  }

  async handleCallback(code: string, userId: string): Promise<void> {
    const clientId = this.configService.get<string>('SLACK_CLIENT_ID') || '';
    const clientSecret = this.configService.get<string>('SLACK_CLIENT_SECRET') || '';
    const redirectUri = this.configService.get<string>('SLACK_REDIRECT_URI') || '';

    const slack = new WebClient();

    try {
      const result = await slack.oauth.v2.access({
        client_id: clientId,
        client_secret: clientSecret,
        code,
        redirect_uri: redirectUri,
      });

      if (result.ok && result.access_token) {
        // Encrypt and store the token
        const encryptedToken = this.encrypt(result.access_token);
        
        await this.prisma.user.update({
          where: { id: userId },
          data: {
            slackToken: encryptedToken,
            slackUserId: result.authed_user?.id,
            slackTeamId: result.team?.id,
          },
        });
      }
    } catch (error) {
      console.error('Slack OAuth error:', error);
      throw new Error('Failed to connect to Slack');
    }
  }

  async disconnectSlack(userId: string): Promise<void> {
    await this.prisma.user.update({
      where: { id: userId },
      data: {
        slackToken: null,
        slackUserId: null,
        slackTeamId: null,
      },
    });
  }

  async getSlackStatus(userId: string): Promise<{ connected: boolean; userId?: string }> {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { slackToken: true, slackUserId: true },
    });

    return {
      connected: !!user?.slackToken,
      userId: user?.slackUserId || undefined,
    };
  }

  async sendUpdateMessage(update: Update & { user: User }): Promise<void> {
    if (!update.user.slackToken) {
      return; // User hasn't connected Slack
    }

    try {
      const decryptedToken = this.decrypt(update.user.slackToken);
      const slack = new WebClient(decryptedToken);

      const date = new Date(update.date).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });

      const message = `📊 Daily Update - ${date}
- Tickets: ${update.tickets}
- Chats: ${update.chats}
- GitHub Issues: ${update.issues}
- Reviews: ${update.reviews}`;

      await slack.chat.postMessage({
        channel: '#updates', // You might want to make this configurable
        text: message,
      });
    } catch (error) {
      console.error('Failed to send Slack message:', error);
      throw error;
    }
  }
}
