import { Controller, Get, Post, Query, UseGuards, Request } from '@nestjs/common';
import { SlackService } from './slack.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@Controller('slack')
@UseGuards(JwtAuthGuard)
export class SlackController {
  constructor(private readonly slackService: SlackService) {}

  @Get('connect')
  async getConnectUrl() {
    const url = await this.slackService.getAuthUrl();
    return { url };
  }

  @Get('callback')
  async handleCallback(@Query('code') code: string, @Request() req) {
    await this.slackService.handleCallback(code, req.user.userId);
    return { success: true };
  }

  @Post('disconnect')
  async disconnect(@Request() req) {
    await this.slackService.disconnectSlack(req.user.userId);
    return { success: true };
  }

  @Get('status')
  async getStatus(@Request() req) {
    return this.slackService.getSlackStatus(req.user.userId);
  }
}
