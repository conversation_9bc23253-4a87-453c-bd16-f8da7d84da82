import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { SlackService } from '../slack/slack.service';
import { Update } from '@prisma/client';
import { CreateUpdateDto, UpdateUpdateDto } from './dto/update.dto';

@Injectable()
export class UpdateService {
  constructor(
    private prisma: PrismaService,
    private slackService: SlackService,
  ) {}

  async create(userId: string, createUpdateDto: CreateUpdateDto): Promise<Update> {
    // Check if update already exists for this date
    const existingUpdate = await this.prisma.update.findUnique({
      where: {
        userId_date: {
          userId,
          date: new Date(createUpdateDto.date),
        },
      },
    });

    if (existingUpdate) {
      throw new ConflictException('Update already exists for this date');
    }

    // Create the update
    const update = await this.prisma.update.create({
      data: {
        userId,
        date: new Date(createUpdateDto.date),
        tickets: createUpdateDto.tickets,
        chats: createUpdateDto.chats,
        issues: createUpdateDto.issues,
        reviews: createUpdateDto.reviews,
      },
      include: {
        user: true,
      },
    });

    // Send Slack message
    try {
      await this.slackService.sendUpdateMessage(update);
    } catch (error) {
      console.error('Failed to send Slack message:', error);
      // Don't fail the update creation if Slack fails
    }

    return update;
  }

  async findAll(
    userId?: string,
    startDate?: string,
    endDate?: string,
  ): Promise<Update[]> {
    const where: any = {};

    if (userId) {
      where.userId = userId;
    }

    if (startDate || endDate) {
      where.date = {};
      if (startDate) {
        where.date.gte = new Date(startDate);
      }
      if (endDate) {
        where.date.lte = new Date(endDate);
      }
    }

    return this.prisma.update.findMany({
      where,
      include: {
        user: true,
      },
      orderBy: {
        date: 'desc',
      },
    });
  }

  async findOne(id: string): Promise<Update> {
    const update = await this.prisma.update.findUnique({
      where: { id },
      include: {
        user: true,
      },
    });

    if (!update) {
      throw new NotFoundException('Update not found');
    }

    return update;
  }

  async update(id: string, userId: string, updateUpdateDto: UpdateUpdateDto): Promise<Update> {
    const existingUpdate = await this.prisma.update.findUnique({
      where: { id },
    });

    if (!existingUpdate) {
      throw new NotFoundException('Update not found');
    }

    if (existingUpdate.userId !== userId) {
      throw new NotFoundException('Update not found');
    }

    return this.prisma.update.update({
      where: { id },
      data: updateUpdateDto,
      include: {
        user: true,
      },
    });
  }

  async remove(id: string, userId: string): Promise<void> {
    const existingUpdate = await this.prisma.update.findUnique({
      where: { id },
    });

    if (!existingUpdate) {
      throw new NotFoundException('Update not found');
    }

    if (existingUpdate.userId !== userId) {
      throw new NotFoundException('Update not found');
    }

    await this.prisma.update.delete({
      where: { id },
    });
  }
}
