import { IsDateString, IsInt, Min, IsOptional } from 'class-validator';

export class CreateUpdateDto {
  @IsDateString()
  date: string;

  @IsInt()
  @Min(0)
  tickets: number;

  @IsInt()
  @Min(0)
  chats: number;

  @IsInt()
  @Min(0)
  issues: number;

  @IsInt()
  @Min(0)
  reviews: number;
}

export class UpdateUpdateDto {
  @IsOptional()
  @IsInt()
  @Min(0)
  tickets?: number;

  @IsOptional()
  @IsInt()
  @Min(0)
  chats?: number;

  @IsOptional()
  @IsInt()
  @Min(0)
  issues?: number;

  @IsOptional()
  @IsInt()
  @Min(0)
  reviews?: number;
}
