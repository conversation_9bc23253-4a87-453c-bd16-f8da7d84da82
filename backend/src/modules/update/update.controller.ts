import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Request,
  Query,
} from '@nestjs/common';
import { UpdateService } from './update.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CreateUpdateDto, UpdateUpdateDto } from './dto/update.dto';

@Controller('updates')
@UseGuards(JwtAuthGuard)
export class UpdateController {
  constructor(private readonly updateService: UpdateService) {}

  @Post()
  create(@Request() req, @Body() createUpdateDto: CreateUpdateDto) {
    return this.updateService.create(req.user.userId, createUpdateDto);
  }

  @Get()
  findAll(
    @Request() req,
    @Query('userId') userId?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    // Regular users can only see their own updates
    const finalUserId = req.user.role === 'USER' ? req.user.userId : userId;
    return this.updateService.findAll(finalUserId, startDate, endDate);
  }

  @Get(':id')
  findOne(@Request() req, @Param('id') id: string) {
    return this.updateService.findOne(id);
  }

  @Patch(':id')
  update(
    @Request() req,
    @Param('id') id: string,
    @Body() updateUpdateDto: UpdateUpdateDto,
  ) {
    return this.updateService.update(id, req.user.userId, updateUpdateDto);
  }

  @Delete(':id')
  remove(@Request() req, @Param('id') id: string) {
    return this.updateService.remove(id, req.user.userId);
  }
}
