import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  // Create test users
  const user1 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      name: '<PERSON>',
      email: '<EMAIL>',
      role: 'USER',
    },
  })

  const user2 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      name: '<PERSON>',
      email: '<EMAIL>',
      role: 'USER',
    },
  })

  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      name: 'Admin User',
      email: '<EMAIL>',
      role: 'ADMIN',
    },
  })

  // Create some sample updates
  const today = new Date()
  const yesterday = new Date(today)
  yesterday.setDate(yesterday.getDate() - 1)
  const twoDaysAgo = new Date(today)
  twoDaysAgo.setDate(twoDaysAgo.getDate() - 2)

  // Create updates individually to handle duplicates
  try {
    await prisma.update.create({
      data: {
        userId: user1.id,
        date: today,
        tickets: 12,
        chats: 8,
        issues: 3,
        reviews: 2,
      },
    })
  } catch (e) {
    // Ignore duplicate errors
  }

  try {
    await prisma.update.create({
      data: {
        userId: user1.id,
        date: yesterday,
        tickets: 15,
        chats: 10,
        issues: 5,
        reviews: 1,
      },
    })
  } catch (e) {
    // Ignore duplicate errors
  }

  try {
    await prisma.update.create({
      data: {
        userId: user2.id,
        date: today,
        tickets: 8,
        chats: 12,
        issues: 2,
        reviews: 4,
      },
    })
  } catch (e) {
    // Ignore duplicate errors
  }

  try {
    await prisma.update.create({
      data: {
        userId: user2.id,
        date: yesterday,
        tickets: 10,
        chats: 15,
        issues: 1,
        reviews: 3,
      },
    })
  } catch (e) {
    // Ignore duplicate errors
  }

  // Create some sample meetings
  try {
    await prisma.meeting.create({
      data: {
        userId: user1.id,
        title: 'Daily Standup',
        date: today,
        timezone: 'UTC',
        meetingLink: 'https://meet.google.com/abc-123',
        metadata: { duration: '30min', type: 'standup' },
      },
    })
  } catch (e) {
    // Ignore duplicate errors
  }

  try {
    await prisma.meeting.create({
      data: {
        userId: user1.id,
        title: 'Sprint Planning',
        date: yesterday,
        timezone: 'UTC',
        meetingLink: 'https://meet.google.com/def-456',
        metadata: { duration: '60min', type: 'planning' },
      },
    })
  } catch (e) {
    // Ignore duplicate errors
  }

  try {
    await prisma.meeting.create({
      data: {
        userId: user2.id,
        title: 'Code Review',
        date: today,
        timezone: 'UTC',
        meetingLink: 'https://meet.google.com/ghi-789',
        metadata: { duration: '45min', type: 'review' },
      },
    })
  } catch (e) {
    // Ignore duplicate errors
  }

  console.log('Seed data created successfully!')
  console.log('Users created:', { user1, user2, admin })
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
