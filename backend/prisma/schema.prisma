// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id          String   @id @default(cuid())
  name        String
  email       String   @unique
  role        Role     @default(USER)
  slackToken  String?  // encrypted slack token
  slackUserId String?  // slack user id
  slackTeamId String?  // slack team id
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  updates     Update[]
  meetings    Meeting[]

  @@map("users")
}

model Update {
  id        String   @id @default(cuid())
  userId    String
  date      DateTime
  tickets   Int      @default(0)
  chats     Int      @default(0)
  issues    Int      @default(0)
  reviews   Int      @default(0)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, date])
  @@map("updates")
}

model Meeting {
  id          String   @id @default(cuid())
  userId      String
  title       String
  date        DateTime
  timezone    String
  meetingLink String?
  metadata    Json?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("meetings")
}

enum Role {
  USER
  ADMIN
  SUPERADMIN
}
