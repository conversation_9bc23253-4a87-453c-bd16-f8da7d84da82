import { JwtService } from '@nestjs/jwt';
import { PrismaService } from '../prisma/prisma.service';
import { User } from '@prisma/client';
export declare class AuthService {
    private prisma;
    private jwtService;
    constructor(prisma: PrismaService, jwtService: JwtService);
    register(name: string, email: string, password: string): Promise<{
        user: User;
        token: string;
    }>;
    login(email: string, password: string): Promise<{
        user: User;
        token: string;
    }>;
    validateUser(userId: string): Promise<User | null>;
    getUserProfile(userId: string): Promise<User>;
}
