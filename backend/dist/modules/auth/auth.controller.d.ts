import { AuthService } from './auth.service';
import { CreateUserDto, LoginDto } from './dto/auth.dto';
export declare class AuthController {
    private authService;
    constructor(authService: AuthService);
    register(createUserDto: CreateUserDto): Promise<{
        user: import(".prisma/client").User;
        token: string;
    }>;
    login(loginDto: LoginDto): Promise<{
        user: import(".prisma/client").User;
        token: string;
    }>;
    getProfile(req: any): Promise<{
        name: string;
        id: string;
        email: string;
        role: import(".prisma/client").$Enums.Role;
        slackToken: string | null;
        slackUserId: string | null;
        slackTeamId: string | null;
        createdAt: Date;
        updatedAt: Date;
    }>;
}
