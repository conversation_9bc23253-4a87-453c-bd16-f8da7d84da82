import { PrismaService } from '../prisma/prisma.service';
export interface AnalyticsData {
    totalTickets: number;
    totalChats: number;
    totalIssues: number;
    totalReviews: number;
    totalMeetings: number;
    dailyStats: Array<{
        date: string;
        tickets: number;
        chats: number;
        issues: number;
        reviews: number;
        meetings: number;
    }>;
}
export interface LeaderboardEntry {
    user: {
        id: string;
        name: string;
        email: string;
    };
    totalTickets: number;
    totalChats: number;
    totalIssues: number;
    totalReviews: number;
    totalScore: number;
}
export declare class AnalyticsService {
    private prisma;
    constructor(prisma: PrismaService);
    getAnalytics(userId?: string, startDate?: string, endDate?: string): Promise<AnalyticsData>;
    getLeaderboard(startDate?: string, endDate?: string): Promise<LeaderboardEntry[]>;
    exportData(userId?: string, startDate?: string, endDate?: string, format?: 'csv' | 'json'): Promise<any>;
}
