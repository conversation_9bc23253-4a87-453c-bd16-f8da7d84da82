"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyticsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let AnalyticsService = class AnalyticsService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async getAnalytics(userId, startDate, endDate) {
        const where = {};
        if (userId) {
            where.userId = userId;
        }
        if (startDate || endDate) {
            where.date = {};
            if (startDate) {
                where.date.gte = new Date(startDate);
            }
            if (endDate) {
                where.date.lte = new Date(endDate);
            }
        }
        const updates = await this.prisma.update.findMany({
            where,
            orderBy: { date: 'asc' },
        });
        const meetingWhere = {};
        if (userId) {
            meetingWhere.userId = userId;
        }
        if (startDate || endDate) {
            meetingWhere.date = {};
            if (startDate) {
                meetingWhere.date.gte = new Date(startDate);
            }
            if (endDate) {
                meetingWhere.date.lte = new Date(endDate);
            }
        }
        const meetings = await this.prisma.meeting.findMany({
            where: meetingWhere,
        });
        const totalTickets = updates.reduce((sum, update) => sum + update.tickets, 0);
        const totalChats = updates.reduce((sum, update) => sum + update.chats, 0);
        const totalIssues = updates.reduce((sum, update) => sum + update.issues, 0);
        const totalReviews = updates.reduce((sum, update) => sum + update.reviews, 0);
        const totalMeetings = meetings.length;
        const dailyStatsMap = new Map();
        updates.forEach(update => {
            const dateKey = update.date.toISOString().split('T')[0];
            if (!dailyStatsMap.has(dateKey)) {
                dailyStatsMap.set(dateKey, {
                    date: dateKey,
                    tickets: 0,
                    chats: 0,
                    issues: 0,
                    reviews: 0,
                    meetings: 0,
                });
            }
            const dayStats = dailyStatsMap.get(dateKey);
            dayStats.tickets += update.tickets;
            dayStats.chats += update.chats;
            dayStats.issues += update.issues;
            dayStats.reviews += update.reviews;
        });
        meetings.forEach(meeting => {
            const dateKey = meeting.date.toISOString().split('T')[0];
            if (!dailyStatsMap.has(dateKey)) {
                dailyStatsMap.set(dateKey, {
                    date: dateKey,
                    tickets: 0,
                    chats: 0,
                    issues: 0,
                    reviews: 0,
                    meetings: 0,
                });
            }
            const dayStats = dailyStatsMap.get(dateKey);
            dayStats.meetings += 1;
        });
        const dailyStats = Array.from(dailyStatsMap.values()).sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
        return {
            totalTickets,
            totalChats,
            totalIssues,
            totalReviews,
            totalMeetings,
            dailyStats,
        };
    }
    async getLeaderboard(startDate, endDate) {
        const where = {};
        if (startDate || endDate) {
            where.date = {};
            if (startDate) {
                where.date.gte = new Date(startDate);
            }
            if (endDate) {
                where.date.lte = new Date(endDate);
            }
        }
        const updates = await this.prisma.update.findMany({
            where,
            include: {
                user: {
                    select: {
                        id: true,
                        name: true,
                        email: true,
                    },
                },
            },
        });
        const userStatsMap = new Map();
        updates.forEach(update => {
            const userId = update.user.id;
            if (!userStatsMap.has(userId)) {
                userStatsMap.set(userId, {
                    user: update.user,
                    totalTickets: 0,
                    totalChats: 0,
                    totalIssues: 0,
                    totalReviews: 0,
                    totalScore: 0,
                });
            }
            const userStats = userStatsMap.get(userId);
            userStats.totalTickets += update.tickets;
            userStats.totalChats += update.chats;
            userStats.totalIssues += update.issues;
            userStats.totalReviews += update.reviews;
        });
        const leaderboard = Array.from(userStatsMap.values()).map(entry => {
            entry.totalScore =
                entry.totalTickets * 1 +
                    entry.totalChats * 1 +
                    entry.totalIssues * 2 +
                    entry.totalReviews * 3;
            return entry;
        });
        return leaderboard.sort((a, b) => b.totalScore - a.totalScore);
    }
    async exportData(userId, startDate, endDate, format = 'json') {
        const analytics = await this.getAnalytics(userId, startDate, endDate);
        if (format === 'csv') {
            const csvHeaders = 'Date,Tickets,Chats,Issues,Reviews,Meetings\n';
            const csvData = analytics.dailyStats
                .map(day => `${day.date},${day.tickets},${day.chats},${day.issues},${day.reviews},${day.meetings}`)
                .join('\n');
            return csvHeaders + csvData;
        }
        return analytics;
    }
};
exports.AnalyticsService = AnalyticsService;
exports.AnalyticsService = AnalyticsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], AnalyticsService);
//# sourceMappingURL=analytics.service.js.map