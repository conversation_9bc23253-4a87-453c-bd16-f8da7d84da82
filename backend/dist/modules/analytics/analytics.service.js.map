{"version": 3, "file": "analytics.service.js", "sourceRoot": "", "sources": ["../../../src/modules/analytics/analytics.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA4C;AAC5C,6DAAyD;AAgClD,IAAM,gBAAgB,GAAtB,MAAM,gBAAgB;IACP;IAApB,YAAoB,MAAqB;QAArB,WAAM,GAAN,MAAM,CAAe;IAAG,CAAC;IAE7C,KAAK,CAAC,YAAY,CAChB,MAAe,EACf,SAAkB,EAClB,OAAgB;QAEhB,MAAM,KAAK,GAAQ,EAAE,CAAC;QAEtB,IAAI,MAAM,EAAE,CAAC;YACX,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;QACxB,CAAC;QAED,IAAI,SAAS,IAAI,OAAO,EAAE,CAAC;YACzB,KAAK,CAAC,IAAI,GAAG,EAAE,CAAC;YAChB,IAAI,SAAS,EAAE,CAAC;gBACd,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC;YACvC,CAAC;YACD,IAAI,OAAO,EAAE,CAAC;gBACZ,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC;YACrC,CAAC;QACH,CAAC;QAGD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC;YAChD,KAAK;YACL,OAAO,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;SACzB,CAAC,CAAC;QAGH,MAAM,YAAY,GAAQ,EAAE,CAAC;QAC7B,IAAI,MAAM,EAAE,CAAC;YACX,YAAY,CAAC,MAAM,GAAG,MAAM,CAAC;QAC/B,CAAC;QACD,IAAI,SAAS,IAAI,OAAO,EAAE,CAAC;YACzB,YAAY,CAAC,IAAI,GAAG,EAAE,CAAC;YACvB,IAAI,SAAS,EAAE,CAAC;gBACd,YAAY,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC;YAC9C,CAAC;YACD,IAAI,OAAO,EAAE,CAAC;gBACZ,YAAY,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC;YAC5C,CAAC;QACH,CAAC;QAED,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;YAClD,KAAK,EAAE,YAAY;SACpB,CAAC,CAAC;QAGH,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;QAC9E,MAAM,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAC1E,MAAM,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QAC5E,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;QAC9E,MAAM,aAAa,GAAG,QAAQ,CAAC,MAAM,CAAC;QAGtC,MAAM,aAAa,GAAG,IAAI,GAAG,EAAe,CAAC;QAE7C,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YACvB,MAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACxD,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;gBAChC,aAAa,CAAC,GAAG,CAAC,OAAO,EAAE;oBACzB,IAAI,EAAE,OAAO;oBACb,OAAO,EAAE,CAAC;oBACV,KAAK,EAAE,CAAC;oBACR,MAAM,EAAE,CAAC;oBACT,OAAO,EAAE,CAAC;oBACV,QAAQ,EAAE,CAAC;iBACZ,CAAC,CAAC;YACL,CAAC;YACD,MAAM,QAAQ,GAAG,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YAC5C,QAAQ,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC;YACnC,QAAQ,CAAC,KAAK,IAAI,MAAM,CAAC,KAAK,CAAC;YAC/B,QAAQ,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC;YACjC,QAAQ,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YACzB,MAAM,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACzD,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;gBAChC,aAAa,CAAC,GAAG,CAAC,OAAO,EAAE;oBACzB,IAAI,EAAE,OAAO;oBACb,OAAO,EAAE,CAAC;oBACV,KAAK,EAAE,CAAC;oBACR,MAAM,EAAE,CAAC;oBACT,OAAO,EAAE,CAAC;oBACV,QAAQ,EAAE,CAAC;iBACZ,CAAC,CAAC;YACL,CAAC;YACD,MAAM,QAAQ,GAAG,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YAC5C,QAAQ,CAAC,QAAQ,IAAI,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC;QAEH,MAAM,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAClE,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,CACxD,CAAC;QAEF,OAAO;YACL,YAAY;YACZ,UAAU;YACV,WAAW;YACX,YAAY;YACZ,aAAa;YACb,UAAU;SACX,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,cAAc,CAClB,SAAkB,EAClB,OAAgB;QAEhB,MAAM,KAAK,GAAQ,EAAE,CAAC;QAEtB,IAAI,SAAS,IAAI,OAAO,EAAE,CAAC;YACzB,KAAK,CAAC,IAAI,GAAG,EAAE,CAAC;YAChB,IAAI,SAAS,EAAE,CAAC;gBACd,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC;YACvC,CAAC;YACD,IAAI,OAAO,EAAE,CAAC;gBACZ,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC;YACrC,CAAC;QACH,CAAC;QAED,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC;YAChD,KAAK;YACL,OAAO,EAAE;gBACP,IAAI,EAAE;oBACJ,MAAM,EAAE;wBACN,EAAE,EAAE,IAAI;wBACR,IAAI,EAAE,IAAI;wBACV,KAAK,EAAE,IAAI;qBACZ;iBACF;aACF;SACF,CAAC,CAAC;QAGH,MAAM,YAAY,GAAG,IAAI,GAAG,EAA4B,CAAC;QAEzD,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YACvB,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;YAC9B,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC9B,YAAY,CAAC,GAAG,CAAC,MAAM,EAAE;oBACvB,IAAI,EAAE,MAAM,CAAC,IAAI;oBACjB,YAAY,EAAE,CAAC;oBACf,UAAU,EAAE,CAAC;oBACb,WAAW,EAAE,CAAC;oBACd,YAAY,EAAE,CAAC;oBACf,UAAU,EAAE,CAAC;iBACd,CAAC,CAAC;YACL,CAAC;YAED,MAAM,SAAS,GAAG,YAAY,CAAC,GAAG,CAAC,MAAM,CAAE,CAAC;YAC5C,SAAS,CAAC,YAAY,IAAI,MAAM,CAAC,OAAO,CAAC;YACzC,SAAS,CAAC,UAAU,IAAI,MAAM,CAAC,KAAK,CAAC;YACrC,SAAS,CAAC,WAAW,IAAI,MAAM,CAAC,MAAM,CAAC;YACvC,SAAS,CAAC,YAAY,IAAI,MAAM,CAAC,OAAO,CAAC;QAC3C,CAAC,CAAC,CAAC;QAGH,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;YAEhE,KAAK,CAAC,UAAU;gBACd,KAAK,CAAC,YAAY,GAAG,CAAC;oBACtB,KAAK,CAAC,UAAU,GAAG,CAAC;oBACpB,KAAK,CAAC,WAAW,GAAG,CAAC;oBACrB,KAAK,CAAC,YAAY,GAAG,CAAC,CAAC;YACzB,OAAO,KAAK,CAAC;QACf,CAAC,CAAC,CAAC;QAEH,OAAO,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC;IACjE,CAAC;IAED,KAAK,CAAC,UAAU,CACd,MAAe,EACf,SAAkB,EAClB,OAAgB,EAChB,SAAyB,MAAM;QAE/B,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;QAEtE,IAAI,MAAM,KAAK,KAAK,EAAE,CAAC;YAErB,MAAM,UAAU,GAAG,8CAA8C,CAAC;YAClE,MAAM,OAAO,GAAG,SAAS,CAAC,UAAU;iBACjC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,OAAO,IAAI,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,OAAO,IAAI,GAAG,CAAC,QAAQ,EAAE,CAAC;iBAClG,IAAI,CAAC,IAAI,CAAC,CAAC;YACd,OAAO,UAAU,GAAG,OAAO,CAAC;QAC9B,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;CACF,CAAA;AAjMY,4CAAgB;2BAAhB,gBAAgB;IAD5B,IAAA,mBAAU,GAAE;qCAEiB,8BAAa;GAD9B,gBAAgB,CAiM5B"}