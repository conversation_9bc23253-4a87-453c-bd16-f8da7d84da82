import { Response } from 'express';
import { AnalyticsService, AnalyticsData, LeaderboardEntry } from './analytics.service';
export declare class AnalyticsController {
    private readonly analyticsService;
    constructor(analyticsService: AnalyticsService);
    getAnalytics(req: any, userId?: string, startDate?: string, endDate?: string): Promise<AnalyticsData>;
    getLeaderboard(startDate?: string, endDate?: string): Promise<LeaderboardEntry[]>;
    exportData(userId: string | undefined, startDate: string | undefined, endDate: string | undefined, format: "csv" | "json" | undefined, res: Response): Promise<void>;
}
