import { PrismaService } from '../prisma/prisma.service';
import { Meeting } from '@prisma/client';
import { CreateMeetingDto } from './dto/meeting.dto';
export declare class MeetingService {
    private prisma;
    constructor(prisma: PrismaService);
    createFromWebhook(createMeetingDto: CreateMeetingDto): Promise<Meeting>;
    findAll(userId?: string, startDate?: string, endDate?: string): Promise<Meeting[]>;
    findOne(id: string): Promise<Meeting>;
    findByUser(userId: string, startDate?: string, endDate?: string): Promise<Meeting[]>;
    remove(id: string): Promise<void>;
}
