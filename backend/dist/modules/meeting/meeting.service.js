"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MeetingService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let MeetingService = class MeetingService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async createFromWebhook(createMeetingDto) {
        const user = await this.prisma.user.findUnique({
            where: { email: createMeetingDto.userEmail },
        });
        if (!user) {
            throw new common_1.BadRequestException(`User with email ${createMeetingDto.userEmail} not found`);
        }
        const meeting = await this.prisma.meeting.create({
            data: {
                userId: user.id,
                title: createMeetingDto.title,
                date: new Date(createMeetingDto.datetime),
                timezone: createMeetingDto.timezone,
                meetingLink: createMeetingDto.meetLink,
                metadata: createMeetingDto.metadata || {},
            },
            include: {
                user: true,
            },
        });
        return meeting;
    }
    async findAll(userId, startDate, endDate) {
        const where = {};
        if (userId) {
            where.userId = userId;
        }
        if (startDate || endDate) {
            where.date = {};
            if (startDate) {
                where.date.gte = new Date(startDate);
            }
            if (endDate) {
                where.date.lte = new Date(endDate);
            }
        }
        return this.prisma.meeting.findMany({
            where,
            include: {
                user: true,
            },
            orderBy: {
                date: 'desc',
            },
        });
    }
    async findOne(id) {
        const meeting = await this.prisma.meeting.findUnique({
            where: { id },
            include: {
                user: true,
            },
        });
        if (!meeting) {
            throw new common_1.NotFoundException('Meeting not found');
        }
        return meeting;
    }
    async findByUser(userId, startDate, endDate) {
        const where = { userId };
        if (startDate || endDate) {
            where.date = {};
            if (startDate) {
                where.date.gte = new Date(startDate);
            }
            if (endDate) {
                where.date.lte = new Date(endDate);
            }
        }
        return this.prisma.meeting.findMany({
            where,
            include: {
                user: true,
            },
            orderBy: {
                date: 'desc',
            },
        });
    }
    async remove(id) {
        const meeting = await this.prisma.meeting.findUnique({
            where: { id },
        });
        if (!meeting) {
            throw new common_1.NotFoundException('Meeting not found');
        }
        await this.prisma.meeting.delete({
            where: { id },
        });
    }
};
exports.MeetingService = MeetingService;
exports.MeetingService = MeetingService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], MeetingService);
//# sourceMappingURL=meeting.service.js.map