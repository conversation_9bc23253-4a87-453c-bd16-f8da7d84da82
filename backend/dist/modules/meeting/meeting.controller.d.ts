import { MeetingService } from './meeting.service';
import { CreateMeetingDto } from './dto/meeting.dto';
export declare class MeetingController {
    private readonly meetingService;
    constructor(meetingService: MeetingService);
    createFromWebhook(createMeetingDto: CreateMeetingDto): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        userId: string;
        date: Date;
        title: string;
        timezone: string;
        meetingLink: string | null;
        metadata: import("@prisma/client/runtime/library").JsonValue | null;
    }>;
    findAll(req: any, userId?: string, startDate?: string, endDate?: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        userId: string;
        date: Date;
        title: string;
        timezone: string;
        meetingLink: string | null;
        metadata: import("@prisma/client/runtime/library").JsonValue | null;
    }[]>;
    findOne(id: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        userId: string;
        date: Date;
        title: string;
        timezone: string;
        meetingLink: string | null;
        metadata: import("@prisma/client/runtime/library").JsonValue | null;
    }>;
    remove(id: string): Promise<void>;
}
