import { MeetingService } from './meeting.service';
import { CreateMeetingDto } from './dto/meeting.dto';
export declare class MeetingController {
    private readonly meetingService;
    constructor(meetingService: MeetingService);
    createFromWebhook(createMeetingDto: CreateMeetingDto): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        userId: string;
        date: Date;
        title: string;
        timezone: string;
        metadata: import("@prisma/client/runtime/library").JsonValue | null;
        meetingLink: string | null;
    }>;
    findAll(req: any, userId?: string, startDate?: string, endDate?: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        userId: string;
        date: Date;
        title: string;
        timezone: string;
        metadata: import("@prisma/client/runtime/library").JsonValue | null;
        meetingLink: string | null;
    }[]>;
    findOne(id: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        userId: string;
        date: Date;
        title: string;
        timezone: string;
        metadata: import("@prisma/client/runtime/library").JsonValue | null;
        meetingLink: string | null;
    }>;
    remove(id: string): Promise<void>;
}
