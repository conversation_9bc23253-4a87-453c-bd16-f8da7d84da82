"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const slack_service_1 = require("../slack/slack.service");
let UpdateService = class UpdateService {
    prisma;
    slackService;
    constructor(prisma, slackService) {
        this.prisma = prisma;
        this.slackService = slackService;
    }
    async create(userId, createUpdateDto) {
        const existingUpdate = await this.prisma.update.findUnique({
            where: {
                userId_date: {
                    userId,
                    date: new Date(createUpdateDto.date),
                },
            },
        });
        if (existingUpdate) {
            throw new common_1.ConflictException('Update already exists for this date');
        }
        const update = await this.prisma.update.create({
            data: {
                userId,
                date: new Date(createUpdateDto.date),
                tickets: createUpdateDto.tickets,
                chats: createUpdateDto.chats,
                issues: createUpdateDto.issues,
                reviews: createUpdateDto.reviews,
            },
            include: {
                user: true,
            },
        });
        try {
            await this.slackService.sendUpdateMessage(update);
        }
        catch (error) {
            console.error('Failed to send Slack message:', error);
        }
        return update;
    }
    async findAll(userId, startDate, endDate) {
        const where = {};
        if (userId) {
            where.userId = userId;
        }
        if (startDate || endDate) {
            where.date = {};
            if (startDate) {
                where.date.gte = new Date(startDate);
            }
            if (endDate) {
                where.date.lte = new Date(endDate);
            }
        }
        return this.prisma.update.findMany({
            where,
            include: {
                user: true,
            },
            orderBy: {
                date: 'desc',
            },
        });
    }
    async findOne(id) {
        const update = await this.prisma.update.findUnique({
            where: { id },
            include: {
                user: true,
            },
        });
        if (!update) {
            throw new common_1.NotFoundException('Update not found');
        }
        return update;
    }
    async update(id, userId, updateUpdateDto) {
        const existingUpdate = await this.prisma.update.findUnique({
            where: { id },
        });
        if (!existingUpdate) {
            throw new common_1.NotFoundException('Update not found');
        }
        if (existingUpdate.userId !== userId) {
            throw new common_1.NotFoundException('Update not found');
        }
        return this.prisma.update.update({
            where: { id },
            data: updateUpdateDto,
            include: {
                user: true,
            },
        });
    }
    async remove(id, userId) {
        const existingUpdate = await this.prisma.update.findUnique({
            where: { id },
        });
        if (!existingUpdate) {
            throw new common_1.NotFoundException('Update not found');
        }
        if (existingUpdate.userId !== userId) {
            throw new common_1.NotFoundException('Update not found');
        }
        await this.prisma.update.delete({
            where: { id },
        });
    }
};
exports.UpdateService = UpdateService;
exports.UpdateService = UpdateService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        slack_service_1.SlackService])
], UpdateService);
//# sourceMappingURL=update.service.js.map