import { UpdateService } from './update.service';
import { CreateUpdateDto, UpdateUpdateDto } from './dto/update.dto';
export declare class UpdateController {
    private readonly updateService;
    constructor(updateService: UpdateService);
    create(req: any, createUpdateDto: CreateUpdateDto): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        userId: string;
        date: Date;
        tickets: number;
        chats: number;
        issues: number;
        reviews: number;
    }>;
    findAll(req: any, userId?: string, startDate?: string, endDate?: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        userId: string;
        date: Date;
        tickets: number;
        chats: number;
        issues: number;
        reviews: number;
    }[]>;
    findOne(req: any, id: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        userId: string;
        date: Date;
        tickets: number;
        chats: number;
        issues: number;
        reviews: number;
    }>;
    update(req: any, id: string, updateUpdateDto: UpdateUpdateDto): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        userId: string;
        date: Date;
        tickets: number;
        chats: number;
        issues: number;
        reviews: number;
    }>;
    remove(req: any, id: string): Promise<void>;
}
