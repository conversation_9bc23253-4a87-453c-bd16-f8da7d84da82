"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateController = void 0;
const common_1 = require("@nestjs/common");
const update_service_1 = require("./update.service");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const update_dto_1 = require("./dto/update.dto");
let UpdateController = class UpdateController {
    updateService;
    constructor(updateService) {
        this.updateService = updateService;
    }
    create(req, createUpdateDto) {
        return this.updateService.create(req.user.userId, createUpdateDto);
    }
    findAll(req, userId, startDate, endDate) {
        const finalUserId = req.user.role === 'USER' ? req.user.userId : userId;
        return this.updateService.findAll(finalUserId, startDate, endDate);
    }
    findOne(req, id) {
        return this.updateService.findOne(id);
    }
    update(req, id, updateUpdateDto) {
        return this.updateService.update(id, req.user.userId, updateUpdateDto);
    }
    remove(req, id) {
        return this.updateService.remove(id, req.user.userId);
    }
};
exports.UpdateController = UpdateController;
__decorate([
    (0, common_1.Post)(),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, update_dto_1.CreateUpdateDto]),
    __metadata("design:returntype", void 0)
], UpdateController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Query)('userId')),
    __param(2, (0, common_1.Query)('startDate')),
    __param(3, (0, common_1.Query)('endDate')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String, String]),
    __metadata("design:returntype", void 0)
], UpdateController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", void 0)
], UpdateController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, update_dto_1.UpdateUpdateDto]),
    __metadata("design:returntype", void 0)
], UpdateController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", void 0)
], UpdateController.prototype, "remove", null);
exports.UpdateController = UpdateController = __decorate([
    (0, common_1.Controller)('updates'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [update_service_1.UpdateService])
], UpdateController);
//# sourceMappingURL=update.controller.js.map