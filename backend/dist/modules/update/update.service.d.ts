import { PrismaService } from '../prisma/prisma.service';
import { SlackService } from '../slack/slack.service';
import { Update } from '@prisma/client';
import { CreateUpdateDto, UpdateUpdateDto } from './dto/update.dto';
export declare class UpdateService {
    private prisma;
    private slackService;
    constructor(prisma: PrismaService, slackService: SlackService);
    create(userId: string, createUpdateDto: CreateUpdateDto): Promise<Update>;
    findAll(userId?: string, startDate?: string, endDate?: string): Promise<Update[]>;
    findOne(id: string): Promise<Update>;
    update(id: string, userId: string, updateUpdateDto: UpdateUpdateDto): Promise<Update>;
    remove(id: string, userId: string): Promise<void>;
}
