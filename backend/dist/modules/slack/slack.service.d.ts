import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../prisma/prisma.service';
import { Update, User } from '@prisma/client';
export declare class SlackService {
    private configService;
    private prisma;
    private readonly encryptionKey;
    constructor(configService: ConfigService, prisma: PrismaService);
    private encrypt;
    private decrypt;
    getAuthUrl(): Promise<string>;
    handleCallback(code: string, userId: string): Promise<void>;
    disconnectSlack(userId: string): Promise<void>;
    getSlackStatus(userId: string): Promise<{
        connected: boolean;
        userId?: string;
    }>;
    sendUpdateMessage(update: Update & {
        user: User;
    }): Promise<void>;
}
