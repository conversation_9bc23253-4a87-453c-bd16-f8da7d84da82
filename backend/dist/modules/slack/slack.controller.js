"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SlackController = void 0;
const common_1 = require("@nestjs/common");
const slack_service_1 = require("./slack.service");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
let SlackController = class SlackController {
    slackService;
    constructor(slackService) {
        this.slackService = slackService;
    }
    async getConnectUrl() {
        const url = await this.slackService.getAuthUrl();
        return { url };
    }
    async handleCallback(code, req) {
        await this.slackService.handleCallback(code, req.user.userId);
        return { success: true };
    }
    async disconnect(req) {
        await this.slackService.disconnectSlack(req.user.userId);
        return { success: true };
    }
    async getStatus(req) {
        return this.slackService.getSlackStatus(req.user.userId);
    }
};
exports.SlackController = SlackController;
__decorate([
    (0, common_1.Get)('connect'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], SlackController.prototype, "getConnectUrl", null);
__decorate([
    (0, common_1.Get)('callback'),
    __param(0, (0, common_1.Query)('code')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], SlackController.prototype, "handleCallback", null);
__decorate([
    (0, common_1.Post)('disconnect'),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], SlackController.prototype, "disconnect", null);
__decorate([
    (0, common_1.Get)('status'),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], SlackController.prototype, "getStatus", null);
exports.SlackController = SlackController = __decorate([
    (0, common_1.Controller)('slack'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [slack_service_1.SlackService])
], SlackController);
//# sourceMappingURL=slack.controller.js.map