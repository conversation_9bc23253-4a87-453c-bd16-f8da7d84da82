"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SlackService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const prisma_service_1 = require("../prisma/prisma.service");
const web_api_1 = require("@slack/web-api");
let SlackService = class SlackService {
    configService;
    prisma;
    encryptionKey;
    constructor(configService, prisma) {
        this.configService = configService;
        this.prisma = prisma;
        this.encryptionKey = this.configService.get('ENCRYPTION_KEY') || 'default-encryption-key-32-chars!!';
    }
    encrypt(text) {
        return Buffer.from(text).toString('base64');
    }
    decrypt(encryptedText) {
        return Buffer.from(encryptedText, 'base64').toString('utf8');
    }
    async getAuthUrl() {
        const clientId = this.configService.get('SLACK_CLIENT_ID') || '';
        const redirectUri = this.configService.get('SLACK_REDIRECT_URI') || '';
        const scopes = 'chat:write,users:read';
        return `https://slack.com/oauth/v2/authorize?client_id=${clientId}&scope=${scopes}&redirect_uri=${encodeURIComponent(redirectUri)}`;
    }
    async handleCallback(code, userId) {
        const clientId = this.configService.get('SLACK_CLIENT_ID') || '';
        const clientSecret = this.configService.get('SLACK_CLIENT_SECRET') || '';
        const redirectUri = this.configService.get('SLACK_REDIRECT_URI') || '';
        const slack = new web_api_1.WebClient();
        try {
            const result = await slack.oauth.v2.access({
                client_id: clientId,
                client_secret: clientSecret,
                code,
                redirect_uri: redirectUri,
            });
            if (result.ok && result.access_token) {
                const encryptedToken = this.encrypt(result.access_token);
                await this.prisma.user.update({
                    where: { id: userId },
                    data: {
                        slackToken: encryptedToken,
                        slackUserId: result.authed_user?.id,
                        slackTeamId: result.team?.id,
                    },
                });
            }
        }
        catch (error) {
            console.error('Slack OAuth error:', error);
            throw new Error('Failed to connect to Slack');
        }
    }
    async disconnectSlack(userId) {
        await this.prisma.user.update({
            where: { id: userId },
            data: {
                slackToken: null,
                slackUserId: null,
                slackTeamId: null,
            },
        });
    }
    async getSlackStatus(userId) {
        const user = await this.prisma.user.findUnique({
            where: { id: userId },
            select: { slackToken: true, slackUserId: true },
        });
        return {
            connected: !!user?.slackToken,
            userId: user?.slackUserId || undefined,
        };
    }
    async sendUpdateMessage(update) {
        if (!update.user.slackToken) {
            return;
        }
        try {
            const decryptedToken = this.decrypt(update.user.slackToken);
            const slack = new web_api_1.WebClient(decryptedToken);
            const date = new Date(update.date).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
            });
            const message = `📊 Daily Update - ${date}
- Tickets: ${update.tickets}
- Chats: ${update.chats}
- GitHub Issues: ${update.issues}
- Reviews: ${update.reviews}`;
            await slack.chat.postMessage({
                channel: '#updates',
                text: message,
            });
        }
        catch (error) {
            console.error('Failed to send Slack message:', error);
            throw error;
        }
    }
};
exports.SlackService = SlackService;
exports.SlackService = SlackService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService,
        prisma_service_1.PrismaService])
], SlackService);
//# sourceMappingURL=slack.service.js.map