import { SlackService } from './slack.service';
export declare class SlackController {
    private readonly slackService;
    constructor(slackService: SlackService);
    getConnectUrl(): Promise<{
        url: string;
    }>;
    handleCallback(code: string, req: any): Promise<{
        success: boolean;
    }>;
    disconnect(req: any): Promise<{
        success: boolean;
    }>;
    getStatus(req: any): Promise<{
        connected: boolean;
        userId?: string;
    }>;
}
