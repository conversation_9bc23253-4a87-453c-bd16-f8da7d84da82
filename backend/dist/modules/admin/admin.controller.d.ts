import { AdminService } from './admin.service';
import { UpdateUserRoleDto } from './dto/admin.dto';
export declare class AdminController {
    private readonly adminService;
    constructor(adminService: AdminService);
    getAllUsers(): Promise<{
        name: string;
        id: string;
        email: string;
        role: import(".prisma/client").$Enums.Role;
        slackToken: string | null;
        slackUserId: string | null;
        slackTeamId: string | null;
        createdAt: Date;
        updatedAt: Date;
    }[]>;
    getUserById(id: string): Promise<{
        name: string;
        id: string;
        email: string;
        role: import(".prisma/client").$Enums.Role;
        slackToken: string | null;
        slackUserId: string | null;
        slackTeamId: string | null;
        createdAt: Date;
        updatedAt: Date;
    }>;
    updateUserRole(id: string, updateUserRoleDto: UpdateUserRoleDto, req: any): Promise<{
        name: string;
        id: string;
        email: string;
        role: import(".prisma/client").$Enums.Role;
        slackToken: string | null;
        slackUserId: string | null;
        slackTeamId: string | null;
        createdAt: Date;
        updatedAt: Date;
    }>;
    deleteUser(id: string, req: any): Promise<void>;
    getUserStats(): Promise<{
        totalUsers: number;
        usersByRole: Record<import(".prisma/client").Role, number>;
        recentUsers: import(".prisma/client").User[];
    }>;
}
