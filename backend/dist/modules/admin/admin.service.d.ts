import { PrismaService } from '../prisma/prisma.service';
import { User, Role } from '@prisma/client';
export declare class AdminService {
    private prisma;
    constructor(prisma: PrismaService);
    getAllUsers(): Promise<User[]>;
    getUserById(id: string): Promise<User>;
    updateUserRole(id: string, role: Role, adminRole: Role): Promise<User>;
    deleteUser(id: string, adminRole: Role): Promise<void>;
    getUserStats(): Promise<{
        totalUsers: number;
        usersByRole: Record<Role, number>;
        recentUsers: User[];
    }>;
}
