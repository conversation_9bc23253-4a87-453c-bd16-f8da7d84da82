"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let AdminService = class AdminService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async getAllUsers() {
        return this.prisma.user.findMany({
            orderBy: {
                createdAt: 'desc',
            },
        });
    }
    async getUserById(id) {
        const user = await this.prisma.user.findUnique({
            where: { id },
        });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        return user;
    }
    async updateUserRole(id, role, adminRole) {
        if ((role === 'ADMIN' || role === 'SUPERADMIN') && adminRole !== 'SUPERADMIN') {
            throw new common_1.ForbiddenException('Only SUPERADMIN can assign ADMIN or SUPERADMIN roles');
        }
        const user = await this.prisma.user.findUnique({
            where: { id },
        });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        return this.prisma.user.update({
            where: { id },
            data: { role },
        });
    }
    async deleteUser(id, adminRole) {
        const user = await this.prisma.user.findUnique({
            where: { id },
        });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        if (user.role === 'SUPERADMIN' && adminRole !== 'SUPERADMIN') {
            throw new common_1.ForbiddenException('Only SUPERADMIN can delete SUPERADMIN users');
        }
        await this.prisma.user.delete({
            where: { id },
        });
    }
    async getUserStats() {
        const totalUsers = await this.prisma.user.count();
        const usersByRole = await this.prisma.user.groupBy({
            by: ['role'],
            _count: {
                role: true,
            },
        });
        const roleStats = usersByRole.reduce((acc, item) => {
            acc[item.role] = item._count.role;
            return acc;
        }, {});
        const allRoles = ['USER', 'ADMIN', 'SUPERADMIN'];
        allRoles.forEach(role => {
            if (!roleStats[role]) {
                roleStats[role] = 0;
            }
        });
        const recentUsers = await this.prisma.user.findMany({
            take: 10,
            orderBy: {
                createdAt: 'desc',
            },
        });
        return {
            totalUsers,
            usersByRole: roleStats,
            recentUsers,
        };
    }
};
exports.AdminService = AdminService;
exports.AdminService = AdminService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], AdminService);
//# sourceMappingURL=admin.service.js.map