export interface User {
  id: string
  name: string
  email: string
  role: 'USER' | 'ADMIN' | 'SUPERADMIN'
  slackToken?: string
  slackUserId?: string
  slackTeamId?: string
  createdAt: string
  updatedAt: string
}

export interface Update {
  id: string
  userId: string
  date: string
  tickets: number
  chats: number
  issues: number
  reviews: number
  createdAt: string
  updatedAt: string
  user?: User
}

export interface Meeting {
  id: string
  userId: string
  title: string
  date: string
  timezone: string
  meetingLink?: string
  metadata?: Record<string, any>
  createdAt: string
  updatedAt: string
  user?: User
}

export interface CreateUpdateRequest {
  date: string
  tickets: number
  chats: number
  issues: number
  reviews: number
}

export interface CreateMeetingRequest {
  userEmail: string
  title: string
  datetime: string
  timezone: string
  meetLink?: string
  metadata?: Record<string, any>
}

export interface AnalyticsData {
  totalTickets: number
  totalChats: number
  totalIssues: number
  totalReviews: number
  totalMeetings: number
  dailyStats: Array<{
    date: string
    tickets: number
    chats: number
    issues: number
    reviews: number
    meetings: number
  }>
}

export interface LeaderboardEntry {
  user: User
  totalTickets: number
  totalChats: number
  totalIssues: number
  totalReviews: number
  totalScore: number
}
