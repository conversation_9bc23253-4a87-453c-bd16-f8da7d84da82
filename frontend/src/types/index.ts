export interface User {
  id: string
  name: string
  email: string
  role: 'USER' | 'ADMIN' | 'SUPERADMIN'
  slackToken?: string
  slackUserId?: string
  slackTeamId?: string
  createdAt: string
  updatedAt: string
}

export interface Update {
  id: string
  userId: string
  date: string
  tickets: number
  chats: number
  issues: number
  reviews: number
  createdAt: string
  updatedAt: string
  user?: User
}

export interface Meeting {
  id: string
  userId: string
  title: string
  date: string
  timezone: string
  meetingLink?: string
  metadata?: Record<string, any>
  createdAt: string
  updatedAt: string
  user?: User
}

export interface CreateUpdateRequest {
  date: string
  tickets: number
  chats: number
  issues: number
  reviews: number
}

export interface CreateMeetingRequest {
  userEmail: string
  title: string
  datetime: string
  timezone: string
  meetLink?: string
  metadata?: Record<string, any>
}

// Analytics types moved to ../types/analytics.ts
