import { 
  User, 
  Update, 
  Meeting, 
  CreateUpdateRequest, 
  CreateMeetingRequest, 
  AnalyticsData, 
  LeaderboardEntry 
} from '../types'

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001'

class ApiService {
  private async request<T>(
    endpoint: string, 
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`
    
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    }

    // Add auth token if available
    const token = localStorage.getItem('auth-token')
    if (token) {
      config.headers = {
        ...config.headers,
        Authorization: `Bearer ${token}`,
      }
    }

    const response = await fetch(url, config)
    
    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`)
    }

    return response.json()
  }

  // Auth
  async login(email: string, password: string): Promise<{ user: User; token: string }> {
    return this.request('/auth/login', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
    })
  }

  async register(name: string, email: string, password: string): Promise<{ user: User; token: string }> {
    return this.request('/auth/register', {
      method: 'POST',
      body: JSON.stringify({ name, email, password }),
    })
  }

  async getProfile(): Promise<User> {
    return this.request('/auth/profile')
  }

  // Updates
  async createUpdate(data: CreateUpdateRequest): Promise<Update> {
    return this.request('/updates', {
      method: 'POST',
      body: JSON.stringify(data),
    })
  }

  async getUpdates(userId?: string, startDate?: string, endDate?: string): Promise<Update[]> {
    const params = new URLSearchParams()
    if (userId) params.append('userId', userId)
    if (startDate) params.append('startDate', startDate)
    if (endDate) params.append('endDate', endDate)
    
    return this.request(`/updates?${params.toString()}`)
  }

  async getUpdateById(id: string): Promise<Update> {
    return this.request(`/updates/${id}`)
  }

  async updateUpdate(id: string, data: Partial<CreateUpdateRequest>): Promise<Update> {
    return this.request(`/updates/${id}`, {
      method: 'PATCH',
      body: JSON.stringify(data),
    })
  }

  async deleteUpdate(id: string): Promise<void> {
    return this.request(`/updates/${id}`, {
      method: 'DELETE',
    })
  }

  // Meetings
  async getMeetings(userId?: string, startDate?: string, endDate?: string): Promise<Meeting[]> {
    const params = new URLSearchParams()
    if (userId) params.append('userId', userId)
    if (startDate) params.append('startDate', startDate)
    if (endDate) params.append('endDate', endDate)
    
    return this.request(`/meetings?${params.toString()}`)
  }

  // Analytics
  async getAnalytics(userId?: string, startDate?: string, endDate?: string): Promise<AnalyticsData> {
    const params = new URLSearchParams()
    if (userId) params.append('userId', userId)
    if (startDate) params.append('startDate', startDate)
    if (endDate) params.append('endDate', endDate)
    
    return this.request(`/analytics?${params.toString()}`)
  }

  async getLeaderboard(startDate?: string, endDate?: string): Promise<LeaderboardEntry[]> {
    const params = new URLSearchParams()
    if (startDate) params.append('startDate', startDate)
    if (endDate) params.append('endDate', endDate)
    
    return this.request(`/analytics/leaderboard?${params.toString()}`)
  }

  // Slack
  async connectSlack(): Promise<{ url: string }> {
    return this.request('/slack/connect')
  }

  async disconnectSlack(): Promise<void> {
    return this.request('/slack/disconnect', {
      method: 'POST',
    })
  }

  async getSlackStatus(): Promise<{ connected: boolean; userId?: string }> {
    return this.request('/slack/status')
  }

  // Admin
  async getUsers(): Promise<User[]> {
    return this.request('/admin/users')
  }

  async updateUserRole(userId: string, role: User['role']): Promise<User> {
    return this.request(`/admin/users/${userId}/role`, {
      method: 'PATCH',
      body: JSON.stringify({ role }),
    })
  }
}

export const apiService = new ApiService()
