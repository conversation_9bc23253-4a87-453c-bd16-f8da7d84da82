import { useEffect, useState } from 'react'
import { useAuthStore, useAppStore } from '../store'
import { apiService } from '../services/api'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card'
import { Button } from '../components/ui/button'
import { BarChart3, Calendar, FileText, MessageSquare, GitBranch, Star } from 'lucide-react'
import type { AnalyticsData } from '../types/analytics'

export function DashboardPage() {
  const { user } = useAuthStore()
  const { setLoading, setError } = useAppStore()
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null)
  const [recentUpdates, setRecentUpdates] = useState([])
  const [upcomingMeetings, setUpcomingMeetings] = useState([])

  useEffect(() => {
    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    setLoading(true)
    try {
      // Get analytics for the last 30 days
      const endDate = new Date().toISOString().split('T')[0]
      const startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
      
      const [analyticsData, updatesData, meetingsData] = await Promise.all([
        apiService.getAnalytics(user?.id, startDate, endDate),
        apiService.getUpdates(user?.id, undefined, undefined),
        apiService.getMeetings(user?.id, undefined, undefined)
      ])

      setAnalytics(analyticsData)
      setRecentUpdates(updatesData.slice(0, 5))
      setUpcomingMeetings(meetingsData.slice(0, 5))
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to load dashboard data')
    } finally {
      setLoading(false)
    }
  }

  const stats = [
    {
      title: 'Total Tickets',
      value: analytics?.totalTickets || 0,
      icon: FileText,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100'
    },
    {
      title: 'Total Chats',
      value: analytics?.totalChats || 0,
      icon: MessageSquare,
      color: 'text-green-600',
      bgColor: 'bg-green-100'
    },
    {
      title: 'GitHub Issues',
      value: analytics?.totalIssues || 0,
      icon: GitBranch,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100'
    },
    {
      title: 'Reviews',
      value: analytics?.totalReviews || 0,
      icon: Star,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100'
    },
    {
      title: 'Meetings',
      value: analytics?.totalMeetings || 0,
      icon: Calendar,
      color: 'text-red-600',
      bgColor: 'bg-red-100'
    }
  ]

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
        <p className="mt-2 text-gray-600">
          Welcome back, {user?.name}! Here's your performance overview.
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        {stats.map((stat) => {
          const Icon = stat.icon
          return (
            <Card key={stat.title}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                  </div>
                  <div className={`p-3 rounded-full ${stat.bgColor}`}>
                    <Icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Updates */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Updates</CardTitle>
            <CardDescription>Your latest daily submissions</CardDescription>
          </CardHeader>
          <CardContent>
            {recentUpdates.length > 0 ? (
              <div className="space-y-4">
                {recentUpdates.map((update: any) => (
                  <div key={update.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <p className="font-medium">{new Date(update.date).toLocaleDateString()}</p>
                      <p className="text-sm text-gray-600">
                        {update.tickets} tickets, {update.chats} chats, {update.issues} issues, {update.reviews} reviews
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 text-center py-4">No updates yet</p>
            )}
          </CardContent>
        </Card>

        {/* Upcoming Meetings */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Meetings</CardTitle>
            <CardDescription>Your latest meetings</CardDescription>
          </CardHeader>
          <CardContent>
            {upcomingMeetings.length > 0 ? (
              <div className="space-y-4">
                {upcomingMeetings.map((meeting: any) => (
                  <div key={meeting.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <p className="font-medium">{meeting.title}</p>
                      <p className="text-sm text-gray-600">
                        {new Date(meeting.date).toLocaleString()}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 text-center py-4">No meetings yet</p>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>Common tasks to get you started</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-4">
            <Button>
              <FileText className="mr-2 h-4 w-4" />
              Submit Today's Update
            </Button>
            <Button variant="outline">
              <BarChart3 className="mr-2 h-4 w-4" />
              View Analytics
            </Button>
            <Button variant="outline">
              <Calendar className="mr-2 h-4 w-4" />
              Check Meetings
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
