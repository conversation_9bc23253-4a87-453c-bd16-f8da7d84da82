import { useState, useEffect } from 'react'
import { useAuthStore, useAppStore } from '../store'
import { apiService } from '../services/api'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card'
import { Button } from '../components/ui/button'
import { Input } from '../components/ui/input'
import { Plus, Edit, Trash2 } from 'lucide-react'
import type { Update } from '../types'

export function UpdatesPage() {
  const { user } = useAuthStore()
  const { updates, setUpdates, addUpdate, setLoading, setError } = useAppStore()
  const [showForm, setShowForm] = useState(false)
  const [formData, setFormData] = useState({
    date: new Date().toISOString().split('T')[0],
    tickets: 0,
    chats: 0,
    issues: 0,
    reviews: 0
  })

  useEffect(() => {
    loadUpdates()
  }, [])

  const loadUpdates = async () => {
    setLoading(true)
    try {
      const data = await apiService.getUpdates(user?.id)
      setUpdates(data)
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to load updates')
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    try {
      const newUpdate = await apiService.createUpdate(formData)
      addUpdate(newUpdate)
      setShowForm(false)
      setFormData({
        date: new Date().toISOString().split('T')[0],
        tickets: 0,
        chats: 0,
        issues: 0,
        reviews: 0
      })
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to create update')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Daily Updates</h1>
          <p className="mt-2 text-gray-600">Track your daily progress and activities</p>
        </div>
        <Button onClick={() => setShowForm(true)}>
          <Plus className="mr-2 h-4 w-4" />
          New Update
        </Button>
      </div>

      {/* Create Update Form */}
      {showForm && (
        <Card>
          <CardHeader>
            <CardTitle>Submit Daily Update</CardTitle>
            <CardDescription>Record your activities for today</CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Date
                  </label>
                  <Input
                    type="date"
                    value={formData.date}
                    onChange={(e) => setFormData({ ...formData, date: e.target.value })}
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Support Tickets
                  </label>
                  <Input
                    type="number"
                    min="0"
                    value={formData.tickets}
                    onChange={(e) => setFormData({ ...formData, tickets: parseInt(e.target.value) || 0 })}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Live Chats
                  </label>
                  <Input
                    type="number"
                    min="0"
                    value={formData.chats}
                    onChange={(e) => setFormData({ ...formData, chats: parseInt(e.target.value) || 0 })}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    GitHub Issues
                  </label>
                  <Input
                    type="number"
                    min="0"
                    value={formData.issues}
                    onChange={(e) => setFormData({ ...formData, issues: parseInt(e.target.value) || 0 })}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Reviews
                  </label>
                  <Input
                    type="number"
                    min="0"
                    value={formData.reviews}
                    onChange={(e) => setFormData({ ...formData, reviews: parseInt(e.target.value) || 0 })}
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <Button type="submit">Submit Update</Button>
                <Button type="button" variant="outline" onClick={() => setShowForm(false)}>
                  Cancel
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}

      {/* Updates List */}
      <div className="space-y-4">
        {updates.map((update: Update) => (
          <Card key={update.id}>
            <CardContent className="p-6">
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="font-semibold text-lg">
                    {new Date(update.date).toLocaleDateString('en-US', {
                      weekday: 'long',
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </h3>
                  <div className="mt-2 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="font-medium">Tickets:</span> {update.tickets}
                    </div>
                    <div>
                      <span className="font-medium">Chats:</span> {update.chats}
                    </div>
                    <div>
                      <span className="font-medium">Issues:</span> {update.issues}
                    </div>
                    <div>
                      <span className="font-medium">Reviews:</span> {update.reviews}
                    </div>
                  </div>
                </div>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm">
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button variant="outline" size="sm">
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
        
        {updates.length === 0 && (
          <Card>
            <CardContent className="p-12 text-center">
              <p className="text-gray-500">No updates yet. Create your first daily update!</p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
