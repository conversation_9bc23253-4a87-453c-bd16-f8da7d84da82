import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { User, Update, Meeting, AnalyticsData } from '../types'

interface AuthState {
  user: User | null
  token: string | null
  isAuthenticated: boolean
  login: (user: User, token: string) => void
  logout: () => void
  updateUser: (user: Partial<User>) => void
}

interface AppState {
  updates: Update[]
  meetings: Meeting[]
  analytics: AnalyticsData | null
  isLoading: boolean
  error: string | null
  setUpdates: (updates: Update[]) => void
  addUpdate: (update: Update) => void
  updateUpdate: (id: string, update: Partial<Update>) => void
  removeUpdate: (id: string) => void
  setMeetings: (meetings: Meeting[]) => void
  addMeeting: (meeting: Meeting) => void
  setAnalytics: (analytics: AnalyticsData) => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  clearError: () => void
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      isAuthenticated: false,
      login: (user, token) => {
        localStorage.setItem('auth-token', token)
        set({ user, token, isAuthenticated: true })
      },
      logout: () => {
        localStorage.removeItem('auth-token')
        set({ user: null, token: null, isAuthenticated: false })
      },
      updateUser: (userData) => {
        const currentUser = get().user
        if (currentUser) {
          set({ user: { ...currentUser, ...userData } })
        }
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({ 
        user: state.user, 
        token: state.token, 
        isAuthenticated: state.isAuthenticated 
      }),
    }
  )
)

export const useAppStore = create<AppState>((set, get) => ({
  updates: [],
  meetings: [],
  analytics: null,
  isLoading: false,
  error: null,
  setUpdates: (updates) => set({ updates }),
  addUpdate: (update) => {
    const currentUpdates = get().updates
    set({ updates: [update, ...currentUpdates] })
  },
  updateUpdate: (id, updateData) => {
    const currentUpdates = get().updates
    const updatedUpdates = currentUpdates.map(update => 
      update.id === id ? { ...update, ...updateData } : update
    )
    set({ updates: updatedUpdates })
  },
  removeUpdate: (id) => {
    const currentUpdates = get().updates
    const filteredUpdates = currentUpdates.filter(update => update.id !== id)
    set({ updates: filteredUpdates })
  },
  setMeetings: (meetings) => set({ meetings }),
  addMeeting: (meeting) => {
    const currentMeetings = get().meetings
    set({ meetings: [meeting, ...currentMeetings] })
  },
  setAnalytics: (analytics) => set({ analytics }),
  setLoading: (isLoading) => set({ isLoading }),
  setError: (error) => set({ error }),
  clearError: () => set({ error: null }),
}))
